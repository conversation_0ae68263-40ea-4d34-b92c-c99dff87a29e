![](images/media/image1.png)

**园区管理**

**需求规格说明书**

![](images/media/image2.png)

**广州云趣信息科技有限公司**

**2025年08月**

**版本修订批准记录**

【 历次版本修订记录，版本修订描述重要说明】

|              |            |                                          |          |            |
| ------------ | ---------- | :--------------------------------------: | -------- | ---------- |
| **提交日期** | **版本号** |             **版本修订描述**             | **作者** | **批准人** |
| 2025.7.20    | 1.0        |          园区管理需求说明书初版          | 黄嘉溥   |            |
| 2025.7.24    | 2.0        | 根据不同园区的调研结论丰富完善需规的内容 | 黄嘉溥   |            |
| 2025.7.28    | 3.0        |          需求评审后完善相关需求          | 黄嘉溥   |            |
| 2025.8.8     | 4.0        |               完善应用细节               | 黄嘉溥   | 杨倩男     |
|              |            |                                          |          |            |
|              |            |                                          |          |            |
|              |            |                                          |          |            |
|              |            |                                          |          |            |

**注：版本修订批准记录，如不需要可删除此页.**

**目录**

[1. 项目背景 [1](#项目背景)](#项目背景)

[2. 产品概述 [1](#产品概述)](#产品概述)

[3. 业务功能说明 [2](#业务功能说明)](#业务功能说明)

[**3.1.** **功能模块（一期）** [2](#_Toc204617185)](#_Toc204617185)

[**3.2.** **功能需求描述** [2](#_Toc204617186)](#_Toc204617186)

[**3.2.1.** **来访人员管理模块** [2](#_Toc204617187)](#_Toc204617187)

[**3.2.1.1** **普通访客到访流程** [2](#_Toc204617188)](#_Toc204617188)

[**3.2.1.2** **政府访客到访流程** [4](#_Toc204617189)](#_Toc204617189)

[**3.2.1.3** **施工承包商访客到访流程**
[6](#_Toc204617190)](#_Toc204617190)

[**3.2.1.4** **施工承包商人员变更到访流程**
[7](#_Toc204617191)](#_Toc204617191)

[**3.2.1.5** **到访人员列表** [9](#_Toc204617192)](#_Toc204617192)

[**3.2.2** **住宿管理（可作为二期需求）**
[9](#_Toc204617193)](#_Toc204617193)

[**3.2.2.1** **宿舍管理** [9](#_Toc204617194)](#_Toc204617194)

[**3.2.2.2** **员工住宿管理** [9](#_Toc204617195)](#_Toc204617195)

[**3.2.2.3** **入宿申请** [10](#_Toc204617196)](#_Toc204617196)

[**3.2.2.4** **报修管理** [10](#_Toc204617197)](#_Toc204617197)

[**3.2.3** **接待管理** [11](#_Toc204617198)](#_Toc204617198)

[**3.2.3.1** **访客住宿管理** [11](#_Toc204617199)](#_Toc204617199)

[**3.2.3.2** **访客就餐管理** [11](#_Toc204617200)](#_Toc204617200)

[**3.2.2.** **车辆管理模块** [12](#_Toc204617201)](#_Toc204617201)

[**3.2.2.1** **员工车辆管理** [12](#_Toc204617202)](#_Toc204617202)

[**3.2.2.2** **施工车辆管理** [13](#_Toc204617203)](#_Toc204617203)

[**3.2.2.3** **访客车辆管理** [13](#_Toc204617204)](#_Toc204617204)

[**3.2.2.4** **公司车辆管理** [15](#_Toc204617205)](#_Toc204617205)

[**3.2.3.** **物资管理模块** [17](#_Toc204617206)](#_Toc204617206)

[**3.2.3.1** **办公资产申请** [17](#_Toc204617207)](#_Toc204617207)

[**3.2.3.2** **办公资产管理** [17](#_Toc204617208)](#_Toc204617208)

# 项目背景

当前园区管理系统已具备车辆管理模块（物流货车管理），部分安防与应急保障由EHS系统承担，后续可视情况将EHS系统数据和流程集成进园区管理系统，实现统一管理。

此外，客户当前在会议管理、人脸识别、数据分析等方面存在系统协同和功能完善的诉求，建议结合现有基础逐步打造“人-车-物-安-数一体化的智慧园区管理平台”。

# 产品概述

![](images/media/image3.emf)

**人员管理：**实现员工与访客的全流程识别与管理，覆盖住宿、就餐、培训等多场景的人事联动。

**车辆管理：**构建车辆及车主身份绑定、通行管控、泊车引导及特种车辆使用管理的一体化平台。

**物资和能耗管理：**统一管理办公资产、后勤物资与能源消耗，实现物联网驱动的数字化物资流程。

**安防和应急管理：**整合EHS系统，强化园区监控、异常预警与作业规范，保障人员与园区环境安全。

**平台与数据能力：**打通系统间数据流通，打造统一人脸识别、流程移动审批与智能数据分析平台。

# 业务功能说明

1.  <span id="_Toc204617185" class="anchor"></span>**功能模块（一期）**

**人员管理模块：**访客申请、来访人员管理

**住宿管理模块：**员工住宿管理、宿舍管理、入宿申请、报修管理

**接待管理模块：**访客住宿接待管理、访客就餐管理

**车辆管理模块：**员工车辆管理、施工车辆管理、访客车辆管理、公司车辆管理、访客车申请、停车引导

**物资管理模块：**物资申领、物资统计

2.  <span id="_Toc204617186" class="anchor"></span>**功能需求描述**

    1.  <span id="_Toc204617187"
        class="anchor"></span>**来访人员管理模块**

        1.  <span id="_Toc204617188"
            class="anchor"></span>**普通访客到访流程**

![图示 描述已自动生成](images/media/image4.png)

1.  访客在服务号h5端进行预约到访申请，在【人员到访申请】入口进入；

2.  **【来访入厂申请单】**，由访客主动发起，含如下字段：来访人单位、入厂人员姓名、联系方式、入厂事由、厂内联系人、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区,
    访客类型（参观/商务）、照片；是否驾车，车牌号，同行人数量，同行人姓名、联系方式、照片；是否需要住宿；是否前往饭堂就餐；

3.  **申请单审批流程**按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；

4.  审批后访客可在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；

5.  警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；

6.  警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；

7.  警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；

8.  各项通知：访客发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知访客；警卫在H5的【现场查询】可以收到新增红点提醒；

9.  申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；

    1.  <span id="_Toc204617189"
        class="anchor"></span>**政府访客到访流程**

![图示 描述已自动生成](images/media/image5.png)

1.  员工在园区管理系统【人员到访申请】进行预约到访申请，对接企业微信移动端和pc端；

2.  **【来访入厂申请单】**，由员工发起，含如下字段：带出员工信息（厂内联系人姓名、联系方式、部门）、来访人单位、入厂人员姓名、联系方式、入厂事由、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区,
    访客类型（参观/商务）、照片；是否驾车，车牌号，同行人数量，同行人姓名、联系方式、照片；是否需要住宿；是否前往饭堂就餐；

3.  **申请单审批流程**按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；

4.  审批后访客会收到手机短信，通过短信链接在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；

5.  警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；

6.  警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；

7.  警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；

8.  各项通知：员工发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知施工承包商；警卫在H5的【现场查询】可以收到新增红点提醒；

9.  申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；

    1.  <span id="_Toc204617190"
        class="anchor"></span>**施工承包商访客到访流程**

![](images/media/image6.png)

1.  承包商人员在服务号h5端进行预约到访申请，在【人员到访申请】入口进入；

2.  **【来访入厂申请单】**，由承包商人员主动发起，含如下字段：来访人单位、入厂人员姓名、联系方式、身份证号码、入厂事由、厂内联系人、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区,
    访客类型（施工承包商）、照片；是否驾车，车辆类型（普通客车、施工车）、车牌号，若是施工车，需填写行驶证编号和上传照片；同行人数量，同行人姓名、联系方式、身份证号码、照片；是否需要住宿；是否前往饭堂就餐；

3.  保存后，需完成入场培训并签字后，提交申请单到管理员审批；入场培训可以在【培训管理】配置；

![图形用户界面, 应用程序, 表格 描述已自动生成](images/media/image7.png)

![图形用户界面, 文本, 应用程序, 电子邮件 描述已自动生成](images/media/image8.png)

培训附件支持图文pdf或者视频，支持一种格式的多个文件上传，支持培训最小时长设置；

4.  承包商施工申请单提交后，对接承包商系统数据，通过身份证号匹配人员所属项目；若员工在企业微信主动发起申请，可主动选择施工承包商和项目信息；

5.  **申请单审批流程**按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；

6.  审批后施工承包商访客可在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；

7.  警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；

8.  警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；

9.  警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；

10.  各项通知：施工承包商发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知施工承包商；警卫在H5的【现场查询】可以收到新增红点提醒；

11.  申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；

     1.  <span id="_Toc204617191"
         class="anchor"></span>**施工承包商人员变更到访流程**

<!-- -->

1.  整体流程与施工承包商到访流程一样，访客类型为承包商人员变更；

    1.  <span id="_Toc204617192"
        class="anchor"></span>**到访人员列表-pc端**

<!-- -->

1.  分常规人员到访和施工承包商人员到访，详见原型；

2.  可进行【详情】、【入园签到】、【出园签到】、【进出记录】操作；

【详情】：可查看申请单详情，管理员在详情页内可以进行审批操作；

【入园签到】：可手动对到访人员进行入园签到，点击后弹窗确认，状态变成【已入园】；

【出园签到】：可手动对到访人员进行出园签到，点击后弹窗确认，状态变成【已出园】；若是承包商，注销施工许可证；

【进出记录】：可查看访客或施工承包商人员的进出记录；

1.  <span id="_Toc204617193"
    class="anchor"></span>**住宿管理（作为二期需求）**

    1.  <span id="_Toc204617194" class="anchor"></span>**宿舍管理**

> 1）宿舍列表列表：园区、楼栋、房号、宿舍类型（员工宿舍、接待客房）、宿舍状态（在住、空闲）、最近更新时间；
>
> 2）宿舍管理员可对宿舍列表的进行新增、删除、修改等操作；

2.  <span id="_Toc204617195" class="anchor"></span>**员工住宿管理**

<!-- -->

1.  管理人员在【员工住宿管理】模块查看员工的住宿状态（在职状态、入住状态、押金状态）；

2.  对接系统：HR系统，同步员工的基本信息、在职状态；

3.  管理内容：入宿登记，退宿登记，押金登记；

> 入宿登记：点【新增】，选择员工，选择宿舍，输入押金金额，点击【确认】，完成入宿登记；
>
> 退宿登记：点【退宿】，点击【确认】，完成退宿登记；
>
> 押金登记：当退宿状态为“已退宿”，
> 押金状态为“已缴纳”，点击【退押金】，点击【确认】，押金状态变为“已退还”；

4.  员工住宿列表，字段如下：员工的部门、名字、职位、在职状态（在职、离职）、联系方式、入宿时间、退宿时间、入住状态（已入宿、已退宿）、押金状态（已缴纳、已退还）、操作（退宿、退押金）；

5.  可筛选员工的在职状态，入住状态，押金状态；

    1.  <span id="_Toc204617196" class="anchor"></span>**入宿申请**

<!-- -->

1.  员工可在H5端或PC端园区管理系统提交入住宿舍申请，申请提交后由综管部负责人进行审核；

2.  **【入宿申请单】**，含如下字段：员工姓名、部门、职位、联系方式、入职时间、入住时间、申请原因；

3.  **申请单审批流程**如下：部门主管、综管部负责人审核后，抄送宿舍管理员；可在手机端审批流程；

4.  审批后员工、管理员可在H5端或PC端园区管理系统查看到入宿申请表；

    1.  <span id="_Toc204617197" class="anchor"></span>**报修管理**

<!-- -->

1.  员工可在H5端或PC端园区管理系统提交报修申请，申请提交后由宿舍管理员进行审核，并指派整改人员；

2.  【报修申请单】，含如下字段：员工姓名、部门、职位、联系方式、报修事项、详细描述、相关照片；

3.  **申请单审批流程**如下：宿舍管理员，指派整改人员；可在手机端审批流程；

4.  审批后员工、管理员、整改人员可在H5端或PC端园区管理系统查看到入宿申请表；

    1.  <span id="_Toc204617198" class="anchor"></span>**接待管理**

        1.  **接待申请-h5或者pc**

![图示 描述已自动生成](images/media/image9.png)

![图示 描述已自动生成](images/media/image10.png)

1.  访客在h5提接待申请单，员工可在企业微信移动端h5或者pc端提申请单；

2.  **【住宿接待申请单】**，含如下字段：关联到访单、是否住宿、入住日期、是否就餐、就餐日期；

3.  **申请单审批流程**如下：部门主管、综管部负责人审核后，抄送宿舍管理员（可配置）；管理员可在手机端和pc端进行流程审批；

4.  审批后访客可在H5端园区管理系统查看到接待申请表；

5.  管理员可在住宿管理管理到访人员住宿，查看申请单详情，进行入住登记，退宿登记，以及查看住宿记录；

![图形用户界面, 应用程序, 表格 描述已自动生成](images/media/image11.png)

6.  管理员可在饭堂管理管理到访人员就餐，查看申请单详情；若饭堂对接人脸识别，上传照片对接人脸系统；若饭堂刷卡，点击【申领饭卡】，弹窗确认申领饭卡，“是否申请饭卡”变为“是”；
    点击【归还饭卡】，弹窗确认归还饭卡，“是否申请饭卡”变为“否”；查看就餐记录；

![](images/media/image12.png)

1.  **楼栋管理**

> 1）宿舍列表列表：园区、楼栋、房号、宿舍类型（员工宿舍、接待客房）、宿舍状态（在住、空闲）、最近更新时间；
>
> 2）宿舍管理员可对宿舍列表的进行新增、删除、修改等操作；

![图形用户界面, 应用程序, 表格 描述已自动生成](images/media/image13.png)

1.  <span id="_Toc204617201" class="anchor"></span>**车辆管理模块**

    1.  <span id="_Toc204617202" class="anchor"></span>**员工车辆管理**

<!-- -->

1.  能与HR系统同步，同步员工信息；

2.  新员工入职时可在企业微信h5端或pc端登记车牌，提交停车申请；

![图形用户界面, 应用程序 描述已自动生成](images/media/image14.png)

3.  管理员在移动端对员工车辆停车申请进行审批；

![图形用户界面, 文本, 应用程序 描述已自动生成](images/media/image15.png)

4.  管理员在pc端对员工车辆进行更多管理；在【详情】查看停车申请单，并可对申请单进行审批，编辑管理车辆信息；可上传车辆照片；启用和停用车辆，启用车辆后，车辆状态为有效，停用车辆后，车辆状态为无效；车辆的出入记录，以及车辆的异常记录；

![图形用户界面, 应用程序 描述已自动生成](images/media/image16.png)

5.  对接hr系统，员工离职后车牌自动失效，不能进入厂区内。

    1.  <span id="_Toc204617204" class="anchor"></span>**访客车辆管理**

![图表 描述已自动生成](images/media/image17.png)

1.  访客车辆包括客户车辆、部分供应商车辆等。

2.  来访人员有访客车申请，审批通过后在pc端对访客车进行管理；可查看申请单详情；上传访客车照片；手动核销出园访客车；查看访客车进出记录；查看访客车异常登记情况；

![图形用户界面, 应用程序, 电子邮件 描述已自动生成](images/media/image18.png)

3.  审批后访客可在H5查看到访预约申请表，提供园区停车位图，对于新能源车辆，引导新能源车到相应的车位；签署入园安全培训须知后，生成带有照片的通行二维码；

4.  访客车辆到达厂区后，警卫扫二维码确认到访（判断二维码有效性），记录入园时间，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；警卫拍照上传车辆照片提交后，车辆自动识别放行，并记录车辆入厂时间。

5.  车辆入厂后将车辆入厂信息（车牌、入厂时间），抄送相关部门（警卫、综管部负责人、厂内联系人、联系人部门主管）。

6.  警卫扫二维码确认离场（判断二维码有效性），记录出园时间，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；

7.  车辆离厂时，警卫在h5端进行后备箱异常车辆登记；登记表单（出园时间、出园车辆车牌号、异常情况描述）；

8.  管理员可在访客车管理新增车辆，领导车辆在车辆管理系统登记后，自动放行。

    1.  <span id="_Toc204617203" class="anchor"></span>**施工车辆管理**

<!-- -->

1.  当施工承包商访客申请有施工车时，管理员可在pc端进行管理；可查看申请单详情；上传施工车照片；手动核销出园施工车；查看施工车进出记录；查看施工车异常登记情况；

![图形用户界面, 文本, 应用程序, 电子邮件, 网站 描述已自动生成](images/media/image19.png)

1.  <span id="_Toc204617205" class="anchor"></span>**公司车辆管理**

![图示 描述已自动生成](images/media/image20.png)

1.  在【资源管理】-【公用车资源】登记公司车辆，包括商务用车、货车、观光车、物流车（AGV）、叉车、清洁车等。

![](images/media/image21.png)

1.  可【新增】公用车；查看公用车详情；编辑公用车信息；【停用】或【启用】公用车；【删除】公用车；查看公用车使用记录；

2.  员工可在h5端或pc端提交**【公用车申请单】**申请使用公车，自动带出申请人、申请部门、申请人联系方式；车辆类别（公车、叉车等）、厂区、用车事由、目的地、用车开始时间、用车结束时间、同行人数、同行人姓名、同行人联系方式。

![](images/media/image22.png)

3.  **申请单审批流**如下：部门主管、综管部主管可在h5端或pc端审核，审批通过后，综管部行政文员在pc端查看车辆的使用情况，指派车辆，抄送给申请人。管理员在h5端【车辆管理】进行审批；

> ![](images/media/image15.png)

管理员也可以在pc端【车辆管理】进行审批；

![](images/media/image23.png)

管理员可以查看公用车申请单详情；指定车辆；收回车辆；查看公用车出入纪录；查看公用车异常情况；

![图片包含 图形用户界面 描述已自动生成](images/media/image24.png)

4.  各项通知：员工发起申请单，通过企业微信通知审批管理员；管理员审批完申请单，通过企业微信通知员工和车辆管理员；警卫在H5的【现场查询】可以收到新增红点提醒；

5.  申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，拿车后为【已取车】，还车后为【已还车】；

6.  员工提交**【车辆维保申请单】**申请车辆的维修与保养，填写申请人、申请部门、车辆类别、车辆车牌、维保类别（定期保养/损坏维修/洗车等）、维保事由、维保时间、预计费用等，**申请单审批流**如下：部门主管、综管部行政文员、综管部主管审核。车辆完成维保后，申请人上传维保明细、实际维保费用等，再由综管部进行费用审核。月度结束，综管部行政文员可批量选择【车辆维保申请单】，提交至OA审批。（待定）

7.  员工提交**【车辆加油申请单】**申请车辆加油，填写申请人、申请部门、车辆类别、车辆车牌、加油型号、加油量、预计费用等，**申请单审批流**如下：部门主管、综管部行政文员、综管部主管审核。车辆完成加油后，申请人上传加油明细、实际费用等，再由综管部进行费用审核。月度结束，综管部行政文员可批量选择【车辆加油申请单】，提交至OA审批。（待定）

    1.  <span id="_Toc204617206"
        class="anchor"></span>**物资管理模块（待定）**

        1.  <span id="_Toc204617207"
            class="anchor"></span>**办公资产申请**

![图示 描述已自动生成](images/media/image25.png)

> **H5端**：员工在服务号h5端进行办公资产申请（如文具、劳保用品等）；管理员可在手机端审批流程；
>
> **pc端**：管理人员在【办公资产管理】模块对专办公资产申请进行审批；
>
> **输入**：办公资产申请表；
>
> **处理**：创建办公资产申请记录，办公资产申请审批；
>
> **输出**：办公资产申请列表；数据统计：按部门统计办公资产的日、周、月的使用量；

2.  <span id="_Toc204617208" class="anchor"></span>**办公资产管理**

> 1）办公用品列表：部门、办公用品名称、型号、库存数量、最近更新时间；支持办公用品的增删改；
>
> 2）劳保用品列表：部门、劳保用品名称、型号、库存数量、最近更新时间；支持劳保用品的增删改；



