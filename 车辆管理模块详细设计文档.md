# 车辆管理模块详细设计文档

## 文档信息

| 项目名称 | 车辆管理模块 |
|---------|-------------|
| 模块标识 | yudao-module-vehicle |
| 文档版本 | 2.0 |
| 编写日期 | 2025-08-11 |
| 编写人员 | 边浩澜 |

## 目录

1. [模块概述](#模块概述)
2. [数据库设计](#数据库设计)
3. [API接口设计](#API接口设计)
4. [业务流程设计](#业务流程设计)
5. [前端页面设计](#前端页面设计)
6. [集成方案设计](#集成方案设计)
7. [测试用例设计](#测试用例设计)

## 模块概述

### 业务范围

车辆管理模块负责园区内所有车辆的全生命周期管理，包括员工车辆登记、访客车辆管理、公司车辆调度、车辆进出记录等功能。支持不同类型车辆的差异化管理和智能化调度。

### 核心功能

- **员工车辆管理**：车辆登记、审批、通行证管理
- **公司车辆管理**：车辆档案、维保管理、使用调度
- **车辆申请管理**：公车申请、维保申请、加油申请
- **进出记录管理**：实时记录、车牌识别、异常处理
- **车辆调度管理**：智能分配、使用统计、成本核算
- **维保管理**：保养提醒、维修记录、费用管理

### 技术架构

```mermaid
graph TB
    subgraph "车辆管理模块架构"
        A1[PC管理端] --> B1[Admin Controller]
        A2[H5移动端] --> B2[App Controller]
        
        B1 --> C1[Vehicle Service]
        B2 --> C1
        
        C1 --> D1[Employee Vehicle Service]
        C1 --> D2[Company Vehicle Service]
        C1 --> D3[Application Service]
        C1 --> D4[Record Service]
        
        D1 --> E1[Employee Vehicle Mapper]
        D2 --> E2[Company Vehicle Mapper]
        D3 --> E3[Application Mapper]
        D4 --> E4[Record Mapper]
        
        E1 --> F1[vehicle_employee]
        E2 --> F2[vehicle_company]
        E3 --> F3[vehicle_application]
        E4 --> F4[vehicle_record]
        
        C1 --> G1[Flowable Engine]
        C1 --> G2[Notification Service]
        C1 --> G3[Schedule Service]
    end
```

### 模块结构

```text
yudao-module-vehicle/
├── pom.xml
├── yudao-module-vehicle-api/
│   ├── src/main/java/cn/iocoder/yudao/module/vehicle/
│   │   ├── api/
│   │   │   ├── VehicleEmployeeApi.java
│   │   │   ├── VehicleCompanyApi.java
│   │   │   └── VehicleApplicationApi.java
│   │   ├── enums/
│   │   │   ├── VehicleTypeEnum.java
│   │   │   ├── VehicleStatusEnum.java
│   │   │   └── ApplicationTypeEnum.java
│   │   └── dto/
│   │       ├── VehicleEmployeeDTO.java
│   │       ├── VehicleCompanyDTO.java
│   │       └── VehicleApplicationDTO.java
│   └── pom.xml
└── yudao-module-vehicle-biz/
    ├── src/main/java/cn/iocoder/yudao/module/vehicle/
    │   ├── controller/
    │   │   ├── admin/
    │   │   │   ├── VehicleEmployeeController.java
    │   │   │   ├── VehicleCompanyController.java
    │   │   │   ├── VehicleApplicationController.java
    │   │   │   └── VehicleRecordController.java
    │   │   └── app/
    │   │       ├── AppVehicleApplicationController.java
    │   │       └── AppVehicleRecordController.java
    │   ├── service/
    │   │   ├── employee/
    │   │   │   ├── VehicleEmployeeService.java
    │   │   │   └── VehicleEmployeeServiceImpl.java
    │   │   ├── company/
    │   │   │   ├── VehicleCompanyService.java
    │   │   │   └── VehicleCompanyServiceImpl.java
    │   │   ├── application/
    │   │   │   ├── VehicleApplicationService.java
    │   │   │   └── VehicleApplicationServiceImpl.java
    │   │   └── record/
    │   │       ├── VehicleRecordService.java
    │   │       └── VehicleRecordServiceImpl.java
    │   ├── dal/
    │   │   ├── dataobject/
    │   │   │   ├── VehicleEmployeeDO.java
    │   │   │   ├── VehicleCompanyDO.java
    │   │   │   ├── VehicleApplicationDO.java
    │   │   │   └── VehicleRecordDO.java
    │   │   └── mysql/
    │   │       ├── VehicleEmployeeMapper.java
    │   │       ├── VehicleCompanyMapper.java
    │   │       ├── VehicleApplicationMapper.java
    │   │       └── VehicleRecordMapper.java
    │   ├── convert/
    │   │   ├── VehicleEmployeeConvert.java
    │   │   ├── VehicleCompanyConvert.java
    │   │   └── VehicleApplicationConvert.java
    │   ├── job/
    │   │   ├── VehicleMaintenanceJob.java
    │   │   └── VehicleExpireJob.java
    │   └── framework/
    │       ├── schedule/
    │       │   └── VehicleScheduleService.java
    │       └── notification/
    │           └── VehicleNotificationService.java
    ├── src/main/resources/
    │   ├── mapper/
    │   │   ├── VehicleEmployeeMapper.xml
    │   │   ├── VehicleCompanyMapper.xml
    │   │   └── VehicleApplicationMapper.xml
    │   └── processes/
    │       ├── vehicle_employee_approval.bpmn20.xml
    │       ├── vehicle_company_approval.bpmn20.xml
    │       └── vehicle_maintenance_approval.bpmn20.xml
    └── pom.xml
```

## 数据库设计

### 核心表结构

#### 1. 员工车辆表 (vehicle_employee)

```sql
CREATE TABLE vehicle_employee (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '员工用户ID',
    employee_name VARCHAR(50) NOT NULL COMMENT '员工姓名',
    employee_no VARCHAR(50) COMMENT '员工工号',
    dept_id BIGINT COMMENT '部门ID',
    dept_name VARCHAR(100) COMMENT '部门名称',
    position VARCHAR(50) COMMENT '职位',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    vehicle_brand VARCHAR(50) COMMENT '车辆品牌',
    vehicle_model VARCHAR(50) COMMENT '车辆型号',
    vehicle_color VARCHAR(20) COMMENT '车辆颜色',
    vehicle_type TINYINT COMMENT '车辆类型：1-轿车 2-SUV 3-MPV 4-货车 5-摩托车 6-电动车',
    fuel_type TINYINT COMMENT '燃料类型：1-汽油 2-柴油 3-电动 4-混合动力',
    engine_no VARCHAR(50) COMMENT '发动机号',
    vin_code VARCHAR(50) COMMENT '车架号',
    vehicle_photo VARCHAR(500) COMMENT '车辆照片URL',
    driving_license_photo VARCHAR(500) COMMENT '行驶证照片URL',
    insurance_photo VARCHAR(500) COMMENT '保险单照片URL',
    application_reason VARCHAR(200) COMMENT '申请原因',
    application_time DATETIME COMMENT '申请时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待审批 2-已审批 3-已驳回 4-有效 5-无效 6-已注销',
    approval_result TINYINT COMMENT '审批结果：1-通过 2-驳回',
    approval_reason VARCHAR(200) COMMENT '审批意见',
    approval_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(50) COMMENT '审批人姓名',
    effective_time DATETIME COMMENT '生效时间',
    expire_time DATETIME COMMENT '失效时间',
    annual_fee DECIMAL(8,2) COMMENT '年费',
    deposit_amount DECIMAL(8,2) COMMENT '押金金额',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_vehicle_plate (vehicle_plate, deleted),
    INDEX idx_user_id (user_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_effective_time (effective_time, expire_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工车辆表';
```

#### 2. 公司车辆表 (vehicle_company)

```sql
CREATE TABLE vehicle_company (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    vehicle_name VARCHAR(100) NOT NULL COMMENT '车辆名称',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    vehicle_type TINYINT NOT NULL COMMENT '车辆类型：1-商务用车 2-货车 3-观光车 4-物流车 5-叉车 6-清洁车 7-工程车',
    vehicle_brand VARCHAR(50) COMMENT '车辆品牌',
    vehicle_model VARCHAR(50) COMMENT '车辆型号',
    vehicle_color VARCHAR(20) COMMENT '车辆颜色',
    vehicle_photo VARCHAR(500) COMMENT '车辆照片URL',
    purchase_date DATE COMMENT '购买日期',
    purchase_price DECIMAL(12,2) COMMENT '购买价格',
    license_expire_date DATE COMMENT '行驶证到期日期',
    insurance_expire_date DATE COMMENT '保险到期日期',
    annual_inspection_date DATE COMMENT '年检到期日期',
    maintenance_mileage INT COMMENT '保养里程',
    current_mileage INT COMMENT '当前里程',
    fuel_type TINYINT COMMENT '燃料类型：1-汽油 2-柴油 3-电动 4-混合动力',
    fuel_consumption DECIMAL(5,2) COMMENT '百公里油耗',
    seating_capacity INT COMMENT '座位数',
    load_capacity DECIMAL(8,2) COMMENT '载重量（吨）',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常 2-维修中 3-停用 4-报废 5-出租中',
    responsible_person VARCHAR(50) COMMENT '负责人',
    responsible_phone VARCHAR(20) COMMENT '负责人电话',
    responsible_dept_id BIGINT COMMENT '负责部门ID',
    parking_location VARCHAR(100) COMMENT '停车位置',
    gps_device_no VARCHAR(50) COMMENT 'GPS设备编号',
    last_maintenance_date DATE COMMENT '上次保养日期',
    next_maintenance_date DATE COMMENT '下次保养日期',
    total_cost DECIMAL(12,2) DEFAULT 0 COMMENT '总费用',
    monthly_cost DECIMAL(10,2) DEFAULT 0 COMMENT '月均费用',
    remarks VARCHAR(500) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_vehicle_plate (vehicle_plate, deleted),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_status (status),
    INDEX idx_responsible_dept (responsible_dept_id),
    INDEX idx_expire_dates (license_expire_date, insurance_expire_date, annual_inspection_date),
    INDEX idx_maintenance_date (next_maintenance_date),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司车辆表';
```

#### 3. 车辆申请表 (vehicle_application)

```sql
CREATE TABLE vehicle_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_no VARCHAR(50) NOT NULL COMMENT '申请单号',
    application_type TINYINT NOT NULL COMMENT '申请类型：1-公车使用申请 2-维保申请 3-加油申请 4-年检申请 5-保险申请',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(50) NOT NULL COMMENT '申请人姓名',
    applicant_dept_id BIGINT COMMENT '申请人部门ID',
    applicant_dept_name VARCHAR(100) COMMENT '申请人部门名称',
    applicant_phone VARCHAR(20) COMMENT '申请人电话',

    -- 公车使用申请字段
    vehicle_id BIGINT COMMENT '申请车辆ID',
    vehicle_plate VARCHAR(20) COMMENT '车牌号',
    use_purpose VARCHAR(200) COMMENT '用车目的',
    use_start_time DATETIME COMMENT '用车开始时间',
    use_end_time DATETIME COMMENT '用车结束时间',
    destination VARCHAR(200) COMMENT '目的地',
    passenger_count INT COMMENT '乘车人数',
    passenger_list TEXT COMMENT '乘车人员名单',
    estimated_mileage INT COMMENT '预计里程',
    driver_id BIGINT COMMENT '指定司机ID',
    driver_name VARCHAR(50) COMMENT '指定司机姓名',

    -- 维保申请字段
    maintenance_type TINYINT COMMENT '维保类型：1-定期保养 2-故障维修 3-事故维修 4-改装升级',
    maintenance_reason VARCHAR(500) COMMENT '维保原因',
    maintenance_items TEXT COMMENT '维保项目',
    estimated_cost DECIMAL(10,2) COMMENT '预估费用',
    preferred_garage VARCHAR(100) COMMENT '首选维修厂',
    urgency_level TINYINT DEFAULT 2 COMMENT '紧急程度：1-紧急 2-普通 3-不急',

    -- 加油申请字段
    fuel_type TINYINT COMMENT '燃料类型：1-汽油 2-柴油',
    fuel_amount DECIMAL(8,2) COMMENT '加油量（升）',
    fuel_cost DECIMAL(8,2) COMMENT '加油费用',
    gas_station VARCHAR(100) COMMENT '加油站',
    current_mileage INT COMMENT '当前里程',

    -- 审批流程字段
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待审批 2-已审批 3-已驳回 4-使用中 5-已完成 6-已取消',
    approval_result TINYINT COMMENT '审批结果：1-通过 2-驳回',
    approval_reason VARCHAR(200) COMMENT '审批意见',
    approval_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(50) COMMENT '审批人姓名',

    -- 执行结果字段
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    actual_mileage INT COMMENT '实际里程',
    actual_cost DECIMAL(10,2) COMMENT '实际费用',
    execution_result TEXT COMMENT '执行结果',
    satisfaction_score TINYINT COMMENT '满意度评分（1-5分）',

    -- 流程字段
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',

    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_application_no (application_no),
    INDEX idx_application_type (application_type),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_status (status),
    INDEX idx_use_time (use_start_time, use_end_time),
    INDEX idx_create_time (create_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆申请表';
```

#### 4. 车辆进出记录表 (vehicle_record)

```sql
CREATE TABLE vehicle_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_no VARCHAR(50) NOT NULL COMMENT '记录编号',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    vehicle_type TINYINT COMMENT '车辆类型：1-员工车辆 2-公司车辆 3-访客车辆 4-外来车辆',
    vehicle_id BIGINT COMMENT '关联车辆ID（员工车辆或公司车辆）',
    driver_id BIGINT COMMENT '司机ID',
    driver_name VARCHAR(50) COMMENT '司机姓名',
    driver_phone VARCHAR(20) COMMENT '司机电话',

    -- 进出信息
    record_type TINYINT NOT NULL COMMENT '记录类型：1-入园 2-出园',
    gate_location VARCHAR(50) COMMENT '门岗位置',
    gate_operator_id BIGINT COMMENT '门岗操作员ID',
    gate_operator_name VARCHAR(50) COMMENT '门岗操作员姓名',
    record_time DATETIME NOT NULL COMMENT '记录时间',

    -- 识别信息
    recognition_method TINYINT COMMENT '识别方式：1-车牌识别 2-二维码扫描 3-人工登记 4-IC卡刷卡',
    recognition_confidence DECIMAL(5,2) COMMENT '识别置信度',
    plate_image_url VARCHAR(500) COMMENT '车牌图片URL',
    vehicle_image_url VARCHAR(500) COMMENT '车辆图片URL',

    -- 访问信息
    visit_purpose VARCHAR(200) COMMENT '来访目的',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    estimated_stay_time INT COMMENT '预计停留时间（分钟）',

    -- 停车信息
    parking_area VARCHAR(50) COMMENT '停车区域',
    parking_space VARCHAR(20) COMMENT '停车位',
    parking_fee DECIMAL(8,2) COMMENT '停车费用',

    -- 异常信息
    is_abnormal TINYINT DEFAULT 0 COMMENT '是否异常：0-正常 1-异常',
    abnormal_type TINYINT COMMENT '异常类型：1-无权限进入 2-车牌不符 3-超时停留 4-违规停车',
    abnormal_reason VARCHAR(200) COMMENT '异常原因',
    handle_result VARCHAR(200) COMMENT '处理结果',

    -- 关联信息
    application_id BIGINT COMMENT '关联申请ID',
    visitor_application_id BIGINT COMMENT '关联访客申请ID',

    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_vehicle_plate (vehicle_plate),
    INDEX idx_vehicle_type_id (vehicle_type, vehicle_id),
    INDEX idx_record_type_time (record_type, record_time),
    INDEX idx_gate_location (gate_location),
    INDEX idx_driver_id (driver_id),
    INDEX idx_is_abnormal (is_abnormal),
    INDEX idx_application_id (application_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆进出记录表';
```

#### 5. 车辆维保记录表 (vehicle_maintenance)

```sql
CREATE TABLE vehicle_maintenance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    maintenance_no VARCHAR(50) NOT NULL COMMENT '维保单号',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    maintenance_type TINYINT NOT NULL COMMENT '维保类型：1-定期保养 2-故障维修 3-事故维修 4-改装升级 5-年检 6-保险',
    maintenance_category TINYINT COMMENT '保养类别：1-A级保养 2-B级保养 3-C级保养 4-大修',

    -- 维保信息
    maintenance_date DATE NOT NULL COMMENT '维保日期',
    maintenance_mileage INT COMMENT '维保时里程',
    maintenance_items TEXT COMMENT '维保项目',
    maintenance_description TEXT COMMENT '维保描述',
    maintenance_garage VARCHAR(100) COMMENT '维保厂家',
    maintenance_person VARCHAR(50) COMMENT '维保人员',

    -- 费用信息
    parts_cost DECIMAL(10,2) DEFAULT 0 COMMENT '配件费用',
    labor_cost DECIMAL(10,2) DEFAULT 0 COMMENT '工时费用',
    other_cost DECIMAL(10,2) DEFAULT 0 COMMENT '其他费用',
    total_cost DECIMAL(10,2) DEFAULT 0 COMMENT '总费用',

    -- 质量信息
    quality_rating TINYINT COMMENT '质量评级：1-优秀 2-良好 3-一般 4-较差 5-很差',
    warranty_period INT COMMENT '保修期（天）',
    warranty_mileage INT COMMENT '保修里程',

    -- 状态信息
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-计划中 2-进行中 3-已完成 4-已取消',
    completion_time DATETIME COMMENT '完成时间',

    -- 关联信息
    application_id BIGINT COMMENT '关联申请ID',

    -- 附件信息
    invoice_urls JSON COMMENT '发票图片URL数组',
    report_urls JSON COMMENT '维保报告URL数组',
    photo_urls JSON COMMENT '维保照片URL数组',

    remarks VARCHAR(500) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_maintenance_no (maintenance_no),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_maintenance_type (maintenance_type),
    INDEX idx_maintenance_date (maintenance_date),
    INDEX idx_status (status),
    INDEX idx_application_id (application_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆维保记录表';
```

#### 6. 车辆流程操作记录表 (vehicle_process_operation)

```sql
CREATE TABLE vehicle_process_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    task_name VARCHAR(100) COMMENT '任务名称',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-提交申请 2-部门审批 3-车管审批 4-分配车辆 5-分配司机 6-开始使用 7-结束使用 8-费用结算 9-流程取消 10-流程驳回',
    operation_result TINYINT COMMENT '操作结果：1-通过 2-驳回 3-取消 4-退回修改',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    operator_type TINYINT COMMENT '操作人类型：1-申请人 2-审批人 3-车管员 4-司机 5-系统自动',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    operation_duration INT COMMENT '操作耗时（分钟）',
    operation_content TEXT COMMENT '操作内容描述',
    operation_reason VARCHAR(500) COMMENT '操作原因/备注',
    before_status TINYINT COMMENT '操作前状态',
    after_status TINYINT COMMENT '操作后状态',
    form_data JSON COMMENT '表单数据快照',
    attachment_urls JSON COMMENT '附件URL数组',
    ip_address VARCHAR(50) COMMENT '操作IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理信息',
    device_info VARCHAR(200) COMMENT '设备信息',
    location_info VARCHAR(200) COMMENT '位置信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator_id (operator_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆流程操作记录表';
```

#### 7. 车辆状态变更历史表 (vehicle_status_history)

```sql
CREATE TABLE vehicle_status_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    target_type TINYINT NOT NULL COMMENT '目标类型：1-员工车辆 2-公司车辆 3-车辆申请',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    target_no VARCHAR(50) COMMENT '目标编号（车牌号或申请单号）',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    change_sequence INT NOT NULL COMMENT '变更序号',
    before_status TINYINT COMMENT '变更前状态',
    after_status TINYINT NOT NULL COMMENT '变更后状态',
    status_name VARCHAR(50) COMMENT '状态名称',
    change_reason VARCHAR(200) COMMENT '变更原因',
    change_type TINYINT NOT NULL COMMENT '变更类型：1-正常流转 2-人工干预 3-系统自动 4-异常处理 5-流程回退',
    change_trigger TINYINT COMMENT '变更触发方式：1-用户操作 2-定时任务 3-系统事件 4-外部接口',
    change_person_id BIGINT COMMENT '变更操作人ID',
    change_person_name VARCHAR(50) COMMENT '变更操作人姓名',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    duration_minutes INT COMMENT '在前一状态停留时长（分钟）',
    related_task_id VARCHAR(64) COMMENT '关联的任务ID',
    related_operation_id BIGINT COMMENT '关联的操作记录ID',
    business_data JSON COMMENT '业务数据快照',
    system_variables JSON COMMENT '系统变量快照',
    notification_sent TINYINT DEFAULT 0 COMMENT '是否已发送通知：0-否 1-是',
    notification_time DATETIME COMMENT '通知发送时间',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_target_type_id (target_type, target_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_change_sequence (target_type, target_id, change_sequence),
    INDEX idx_before_status (before_status),
    INDEX idx_after_status (after_status),
    INDEX idx_change_type (change_type),
    INDEX idx_change_person_id (change_person_id),
    INDEX idx_change_time (change_time),
    INDEX idx_related_task_id (related_task_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆状态变更历史表';
```

#### 8. 车辆审批意见表 (vehicle_approval_opinion)

```sql
CREATE TABLE vehicle_approval_opinion (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    approval_level TINYINT NOT NULL COMMENT '审批级别：1-部门主管审批 2-车管部审批 3-财务审批 4-总经理审批',
    approval_sequence INT COMMENT '审批序号（同级多人审批时使用）',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审批人姓名',
    approver_dept_id BIGINT COMMENT '审批人部门ID',
    approver_dept_name VARCHAR(100) COMMENT '审批人部门名称',
    approver_role VARCHAR(50) COMMENT '审批人角色',
    approval_type TINYINT NOT NULL COMMENT '审批类型：1-单人审批 2-会签 3-或签 4-依次审批',
    approval_result TINYINT NOT NULL COMMENT '审批结果：1-通过 2-驳回 3-退回修改 4-转交他人 5-加签',
    approval_time DATETIME NOT NULL COMMENT '审批时间',
    approval_duration INT COMMENT '审批耗时（分钟）',
    opinion_type TINYINT COMMENT '意见类型：1-同意 2-有条件同意 3-不同意 4-建议修改',
    opinion_content TEXT COMMENT '审批意见内容',
    opinion_summary VARCHAR(200) COMMENT '意见摘要',
    conditions_requirements TEXT COMMENT '附加条件或要求',
    cost_approval_amount DECIMAL(10,2) COMMENT '费用审批金额',
    vehicle_assignment VARCHAR(100) COMMENT '车辆分配建议',
    driver_assignment VARCHAR(100) COMMENT '司机分配建议',
    attachment_urls JSON COMMENT '审批附件URL数组',
    signature_image VARCHAR(500) COMMENT '电子签名图片URL',
    delegate_person_id BIGINT COMMENT '被委托人ID（转交时使用）',
    delegate_reason VARCHAR(200) COMMENT '委托原因',
    is_final_approval TINYINT DEFAULT 0 COMMENT '是否为最终审批：0-否 1-是',
    next_approver_suggestion VARCHAR(200) COMMENT '下级审批建议',
    approval_weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '审批权重（会签时使用）',
    ip_address VARCHAR(50) COMMENT '审批IP地址',
    device_info VARCHAR(200) COMMENT '审批设备信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_approval_level (approval_level),
    INDEX idx_approver_id (approver_id),
    INDEX idx_approval_result (approval_result),
    INDEX idx_approval_time (approval_time),
    INDEX idx_is_final_approval (is_final_approval),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆审批意见表';
```

#### 9. 车辆异常处理记录表 (vehicle_exception_record)

```sql
CREATE TABLE vehicle_exception_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    exception_no VARCHAR(50) NOT NULL COMMENT '异常单号',
    exception_type TINYINT NOT NULL COMMENT '异常类型：1-违规停车 2-超时使用 3-未按时归还 4-车辆损坏 5-交通违章 6-事故处理 7-其他异常',
    exception_level TINYINT NOT NULL COMMENT '异常级别：1-低 2-中 3-高 4-紧急',

    -- 关联信息
    vehicle_type TINYINT COMMENT '车辆类型：1-员工车辆 2-公司车辆',
    vehicle_id BIGINT COMMENT '车辆ID',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    application_id BIGINT COMMENT '关联申请ID',
    record_id BIGINT COMMENT '关联进出记录ID',

    -- 异常详情
    exception_title VARCHAR(200) NOT NULL COMMENT '异常标题',
    exception_description TEXT NOT NULL COMMENT '异常详细描述',
    exception_location VARCHAR(200) COMMENT '异常发生地点',
    exception_time DATETIME NOT NULL COMMENT '异常发生时间',
    discovery_time DATETIME COMMENT '异常发现时间',
    discovery_method TINYINT COMMENT '发现方式：1-系统自动检测 2-人工巡查 3-用户举报 4-监控发现',
    discoverer_id BIGINT COMMENT '发现人ID',
    discoverer_name VARCHAR(50) COMMENT '发现人姓名',

    -- 责任人信息
    responsible_person_id BIGINT COMMENT '责任人ID',
    responsible_person_name VARCHAR(50) COMMENT '责任人姓名',
    responsible_person_phone VARCHAR(20) COMMENT '责任人电话',
    responsible_dept_id BIGINT COMMENT '责任部门ID',
    responsible_dept_name VARCHAR(100) COMMENT '责任部门名称',

    -- 处理信息
    handling_status TINYINT DEFAULT 1 COMMENT '处理状态：1-待处理 2-处理中 3-已解决 4-已关闭 5-无需处理',
    handler_id BIGINT COMMENT '处理人ID',
    handler_name VARCHAR(50) COMMENT '处理人姓名',
    handling_start_time DATETIME COMMENT '开始处理时间',
    handling_end_time DATETIME COMMENT '处理完成时间',
    handling_duration INT COMMENT '处理耗时（分钟）',
    handling_method TINYINT COMMENT '处理方式：1-现场处理 2-电话沟通 3-书面通知 4-系统处理 5-其他',
    handling_steps TEXT COMMENT '处理步骤记录',
    handling_result TEXT COMMENT '处理结果描述',

    -- 费用信息
    penalty_amount DECIMAL(10,2) DEFAULT 0 COMMENT '罚款金额',
    repair_cost DECIMAL(10,2) DEFAULT 0 COMMENT '维修费用',
    other_cost DECIMAL(10,2) DEFAULT 0 COMMENT '其他费用',
    total_cost DECIMAL(10,2) DEFAULT 0 COMMENT '总费用',

    -- 预防措施
    prevention_measures TEXT COMMENT '预防措施',
    improvement_suggestions TEXT COMMENT '改进建议',

    -- 附件信息
    evidence_urls JSON COMMENT '证据图片URL数组',
    document_urls JSON COMMENT '相关文档URL数组',

    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_exception_no (exception_no),
    INDEX idx_exception_type (exception_type),
    INDEX idx_exception_level (exception_level),
    INDEX idx_vehicle_plate (vehicle_plate),
    INDEX idx_vehicle_type_id (vehicle_type, vehicle_id),
    INDEX idx_exception_time (exception_time),
    INDEX idx_handling_status (handling_status),
    INDEX idx_responsible_person_id (responsible_person_id),
    INDEX idx_handler_id (handler_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆异常处理记录表';
```

#### 10. 车辆费用记录表 (vehicle_cost_record)

```sql
CREATE TABLE vehicle_cost_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    cost_no VARCHAR(50) NOT NULL COMMENT '费用单号',
    cost_type TINYINT NOT NULL COMMENT '费用类型：1-燃油费 2-维修费 3-保养费 4-保险费 5-年检费 6-停车费 7-过路费 8-罚款 9-其他',

    -- 关联信息
    vehicle_type TINYINT COMMENT '车辆类型：1-员工车辆 2-公司车辆',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    vehicle_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    application_id BIGINT COMMENT '关联申请ID',
    maintenance_id BIGINT COMMENT '关联维保记录ID',

    -- 费用信息
    cost_amount DECIMAL(10,2) NOT NULL COMMENT '费用金额',
    cost_date DATE NOT NULL COMMENT '费用发生日期',
    cost_description VARCHAR(200) COMMENT '费用描述',
    cost_category VARCHAR(50) COMMENT '费用分类',

    -- 报销信息
    reimbursement_status TINYINT DEFAULT 1 COMMENT '报销状态：1-待报销 2-已报销 3-已拒绝 4-无需报销',
    reimbursement_person_id BIGINT COMMENT '报销人ID',
    reimbursement_person_name VARCHAR(50) COMMENT '报销人姓名',
    reimbursement_time DATETIME COMMENT '报销时间',
    reimbursement_amount DECIMAL(10,2) COMMENT '报销金额',

    -- 发票信息
    invoice_no VARCHAR(50) COMMENT '发票号码',
    invoice_date DATE COMMENT '发票日期',
    invoice_amount DECIMAL(10,2) COMMENT '发票金额',
    invoice_url VARCHAR(500) COMMENT '发票图片URL',

    -- 供应商信息
    supplier_name VARCHAR(100) COMMENT '供应商名称',
    supplier_contact VARCHAR(50) COMMENT '供应商联系人',
    supplier_phone VARCHAR(20) COMMENT '供应商电话',

    -- 里程信息
    mileage_before INT COMMENT '费用发生前里程',
    mileage_after INT COMMENT '费用发生后里程',
    mileage_used INT COMMENT '使用里程',

    remarks VARCHAR(500) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_cost_no (cost_no),
    INDEX idx_cost_type (cost_type),
    INDEX idx_vehicle_type_id (vehicle_type, vehicle_id),
    INDEX idx_cost_date (cost_date),
    INDEX idx_reimbursement_status (reimbursement_status),
    INDEX idx_application_id (application_id),
    INDEX idx_maintenance_id (maintenance_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆费用记录表';
```

### 数据库初始化脚本

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS yudao_vehicle DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE yudao_vehicle;

-- 插入车辆类型字典
INSERT INTO system_dict_type (name, type, status, remark) VALUES
('车辆类型', 'vehicle_type', 0, '车辆类型分类'),
('燃料类型', 'fuel_type', 0, '车辆燃料类型'),
('车辆状态', 'vehicle_status', 0, '车辆状态'),
('申请类型', 'application_type', 0, '车辆申请类型'),
('维保类型', 'maintenance_type', 0, '车辆维保类型'),
('异常类型', 'exception_type', 0, '车辆异常类型'),
('费用类型', 'cost_type', 0, '车辆费用类型');

-- 插入车辆类型字典数据
INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '轿车', '1', 'vehicle_type', 0),
(2, 'SUV', '2', 'vehicle_type', 0),
(3, 'MPV', '3', 'vehicle_type', 0),
(4, '货车', '4', 'vehicle_type', 0),
(5, '摩托车', '5', 'vehicle_type', 0),
(6, '电动车', '6', 'vehicle_type', 0);

-- 插入燃料类型字典数据
INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '汽油', '1', 'fuel_type', 0),
(2, '柴油', '2', 'fuel_type', 0),
(3, '电动', '3', 'fuel_type', 0),
(4, '混合动力', '4', 'fuel_type', 0);

-- 插入车辆状态字典数据
INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '待审批', '1', 'vehicle_status', 0),
(2, '已审批', '2', 'vehicle_status', 0),
(3, '已驳回', '3', 'vehicle_status', 0),
(4, '有效', '4', 'vehicle_status', 0),
(5, '无效', '5', 'vehicle_status', 0),
(6, '已注销', '6', 'vehicle_status', 0);
```

### 表关联关系说明

#### 1. 业务表关联关系图

##### A. 核心业务表关联关系图

```mermaid
graph TB
    subgraph "车辆基础数据"
        VE[vehicle_employee<br/>员工车辆表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>user_id: 用户ID<br/>vehicle_plate: 车牌号<br/>status: 状态]

        VC[vehicle_company<br/>公司车辆表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>vehicle_plate: 车牌号<br/>vehicle_brand: 品牌<br/>status: 状态]
    end

    subgraph "申请流程数据"
        VA[vehicle_application<br/>车辆申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>application_type: 申请类型<br/>vehicle_id: 车辆ID<br/>status: 状态]

        VPO[vehicle_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>operation_type: 操作类型<br/>operator_id: 操作人]

        VAO[vehicle_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>approval_level: 审批级别<br/>approval_result: 审批结果]

        VSH[vehicle_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>target_id: 目标ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态]
    end

    subgraph "业务记录数据"
        VR[vehicle_record<br/>进出记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>vehicle_plate: 车牌号<br/>record_type: 记录类型<br/>record_time: 记录时间]

        VM[vehicle_maintenance<br/>维保记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>vehicle_id: 车辆ID<br/>maintenance_type: 维保类型<br/>maintenance_date: 维保日期]

        VER[vehicle_exception_record<br/>异常记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>vehicle_id: 车辆ID<br/>exception_type: 异常类型<br/>exception_time: 异常时间]

        VCR[vehicle_cost_record<br/>费用记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>vehicle_id: 车辆ID<br/>cost_type: 费用类型<br/>cost_amount: 费用金额]
    end

    %% 关联关系
    VA -->|1:N<br/>id = application_id| VPO
    VA -->|1:N<br/>id = application_id| VAO
    VA -->|1:N<br/>id = application_id| VR
    VA -->|1:N<br/>id = application_id| VM
    VA -->|1:N<br/>id = application_id| VER
    VA -->|1:N<br/>id = application_id| VCR

    VC -->|1:N<br/>id = vehicle_id| VA
    VC -->|1:N<br/>id = vehicle_id| VR
    VC -->|1:N<br/>id = vehicle_id| VM
    VC -->|1:N<br/>id = vehicle_id| VER
    VC -->|1:N<br/>id = vehicle_id| VCR

    VE -->|1:N<br/>id = vehicle_id| VR
    VE -->|1:N<br/>id = vehicle_id| VER
    VE -->|1:N<br/>id = vehicle_id| VCR

    VM -->|1:N<br/>id = maintenance_id| VCR

    VPO -.->|1:1<br/>关联操作| VSH

    style VE fill:#e3f2fd
    style VC fill:#e3f2fd
    style VA fill:#f3e5f5
    style VPO fill:#f3e5f5
    style VAO fill:#f3e5f5
    style VSH fill:#f3e5f5
    style VR fill:#e8f5e8
    style VM fill:#e8f5e8
    style VER fill:#e8f5e8
    style VCR fill:#e8f5e8
```

##### B. 数据流向关系图

```mermaid
graph LR
    subgraph "数据输入"
        UI[用户界面]
        API[API接口]
        SYS[系统自动]
    end

    subgraph "核心业务流程"
        APP[车辆申请]
        APPROVAL[审批流程]
        SCHEDULE[车辆调度]
        USAGE[车辆使用]
        MAINTENANCE[维保管理]
    end

    subgraph "数据存储"
        MASTER[主数据表]
        PROCESS[流程记录表]
        BUSINESS[业务记录表]
    end

    subgraph "数据输出"
        REPORT[报表统计]
        NOTIFY[消息通知]
        EXPORT[数据导出]
    end

    UI --> APP
    API --> APP
    SYS --> SCHEDULE

    APP --> APPROVAL
    APPROVAL --> SCHEDULE
    SCHEDULE --> USAGE
    USAGE --> MAINTENANCE

    APP --> MASTER
    APPROVAL --> PROCESS
    SCHEDULE --> PROCESS
    USAGE --> BUSINESS
    MAINTENANCE --> BUSINESS

    MASTER --> REPORT
    PROCESS --> REPORT
    BUSINESS --> REPORT

    PROCESS --> NOTIFY
    BUSINESS --> NOTIFY

    MASTER --> EXPORT
    BUSINESS --> EXPORT

    style UI fill:#ffebee
    style API fill:#ffebee
    style SYS fill:#ffebee
    style REPORT fill:#e8f5e8
    style NOTIFY fill:#e8f5e8
    style EXPORT fill:#e8f5e8
```

#### 2. 业务表与Flowable工作流表关系图

##### A. 流程实例级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        VA[vehicle_application<br/>车辆申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>process_instance_id: 流程实例ID<br/>status: 申请状态]

        VPE[vehicle_process_instance_ext<br/>流程实例扩展表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>process_instance_id: 流程实例ID<br/>business_key: 业务Key<br/>process_status: 流程状态]
    end

    subgraph "Flowable工作流层"
        ARE[ACT_RU_EXECUTION<br/>运行时流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>ACT_ID_: 当前活动ID<br/>START_USER_ID_: 发起人]

        AHP[ACT_HI_PROCINST<br/>历史流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    VA -.->|1:1<br/>process_instance_id| ARE
    VPE -.->|1:1<br/>process_instance_id| AHP
    VA -->|1:1<br/>id = application_id| VPE

    style VA fill:#e1f5fe
    style VPE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

##### B. 任务级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        VAO[vehicle_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>approver_id: 审批人<br/>approval_result: 审批结果]

        VPO[vehicle_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>operation_type: 操作类型<br/>operator_id: 操作人]

        VSH[vehicle_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>target_id: 目标ID<br/>related_task_id: 关联任务ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态]
    end

    subgraph "Flowable工作流层"
        ART[ACT_RU_TASK<br/>运行时任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>TASK_DEF_KEY_: 任务定义Key<br/>ASSIGNEE_: 处理人]

        AHT[ACT_HI_TASKINST<br/>历史任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    VAO -.->|N:1<br/>task_id = ID_| ART
    VPO -.->|N:1<br/>task_id = ID_| AHT
    VSH -.->|N:1<br/>related_task_id = ID_| AHT

    style VAO fill:#e8f5e8
    style VPO fill:#e8f5e8
    style VSH fill:#e8f5e8
    style ART fill:#fff3e0
    style AHT fill:#fff3e0
```

##### C. 业务Key关联关系图

```mermaid
graph LR
    subgraph "业务标识"
        AN[application_no<br/>申请单号<br/>格式: VA20250111001]
    end

    subgraph "业务表"
        VA[vehicle_application<br/>application_no]
        VPE[vehicle_process_instance_ext<br/>business_key]
    end

    subgraph "Flowable表"
        ARE[ACT_RU_EXECUTION<br/>BUSINESS_KEY_]
        AHP[ACT_HI_PROCINST<br/>BUSINESS_KEY_]
    end

    AN --> VA
    AN --> VPE
    AN --> ARE
    AN --> AHP

    style AN fill:#ffebee
    style VA fill:#e1f5fe
    style VPE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

#### 3. 关联关系详细说明

**A. 流程实例级别关联**

**1. vehicle_application 与 ACT_RU_EXECUTION 的关联关系**

这是车辆申请表与Flowable运行时流程实例表之间的核心关联关系。当用户提交车辆申请并启动审批流程时，系统会在Flowable引擎中创建一个流程实例，该实例的ID会存储在`vehicle_application.process_instance_id`字段中。

- **关联字段映射**：`vehicle_application.process_instance_id` = `ACT_RU_EXECUTION.PROC_INST_ID_`
- **关系类型**：1:1（一个车辆申请对应一个流程实例）
- **业务含义**：通过此关联可以查询车辆申请当前的流程状态、当前处理节点、处理人等信息
- **数据流向**：业务操作触发流程引擎，流程状态变化反映到业务数据
- **查询场景**：查询某个申请的当前审批状态、查询某个流程实例对应的业务数据

**2. vehicle_process_instance_ext 与 ACT_HI_PROCINST 的关联关系**

这是业务流程扩展表与Flowable历史流程实例表之间的映射关系。扩展表存储了业务相关的流程信息，与Flowable的历史表形成互补。

- **关联字段映射**：`vehicle_process_instance_ext.process_instance_id` = `ACT_HI_PROCINST.PROC_INST_ID_`
- **关系类型**：1:1（一个业务流程扩展记录对应一个历史流程实例）
- **业务含义**：扩展表补充了Flowable历史表中缺少的业务信息，如SLA状态、升级次数、业务优先级等
- **数据同步**：流程启动时同时创建两个表的记录，流程状态变化时同步更新
- **查询场景**：流程历史分析、SLA监控、流程效率统计

**B. 任务级别关联**

**1. vehicle_approval_opinion 与 ACT_RU_TASK 的关联关系**

这是审批意见表与Flowable运行时任务表之间的关联关系。每当流程流转到需要人工审批的节点时，Flowable会创建一个任务，审批人完成审批后，系统会在审批意见表中记录详细的审批信息。

- **关联字段映射**：`vehicle_approval_opinion.task_id` = `ACT_RU_TASK.ID_`
- **关系类型**：N:1（一个任务可能有多条审批意见，如会签场景）
- **业务含义**：记录每个审批任务的详细审批意见、附件、风险评估等业务信息
- **数据流向**：任务创建时获取任务信息，任务完成时记录审批结果
- **查询场景**：查询某个任务的审批意见、统计审批人的审批历史

**2. vehicle_process_operation 与 ACT_HI_TASKINST 的关联关系**

这是流程操作记录表与Flowable历史任务表之间的关联关系。系统会记录每个任务相关的所有操作，包括任务分配、处理、完成等操作。

- **关联字段映射**：`vehicle_process_operation.task_id` = `ACT_HI_TASKINST.ID_`
- **关系类型**：N:1（一个任务可能有多个操作记录，如分配、处理、完成）
- **业务含义**：记录任务生命周期中的所有操作，包括系统自动操作和人工操作
- **数据流向**：任务状态变化时实时记录操作信息
- **查询场景**：审计追踪、操作历史查询、性能分析

**C. 业务Key关联**

**业务Key的作用和映射关系**

业务Key是连接业务数据和工作流数据的重要桥梁，它提供了一种通过业务标识快速定位流程实例的方式。

**1. vehicle_application.application_no → ACT_RU_EXECUTION.BUSINESS_KEY_**

- **映射关系**：车辆申请单号直接作为Flowable流程实例的业务Key
- **业务含义**：通过申请单号可以快速定位到对应的流程实例
- **数据同步**：流程启动时将申请单号设置为业务Key
- **查询优势**：支持通过申请单号快速查询流程状态，无需知道流程实例ID
- **使用场景**：
  - 车管人员通过申请单号查询审批进度
  - 系统集成时通过业务单号进行数据交换
  - 异常处理时通过申请单号定位问题流程

#### 4. 数据一致性保证

**应用层数据一致性控制：**
- 所有表均不使用数据库外键约束，通过应用层逻辑维护数据一致性
- 使用`@Transactional`注解确保业务操作和相关记录在同一事务中执行
- 异常情况下自动回滚所有相关记录，避免数据不一致

**数据完整性验证：**
- 在Service层进行关联数据的存在性验证
- 删除主记录前检查是否存在关联的子记录
- 提供数据完整性检查工具，定期验证数据关联关系

**级联操作处理：**
- 在应用层实现逻辑删除，避免物理删除导致的数据丢失
- 删除车辆申请时，同步更新所有关联记录的状态
- 提供批量数据清理功能，定期清理过期的无效数据

**审计追踪：**
- 所有数据变更操作都有完整的审计记录
- 支持按时间、操作人、操作类型等维度查询
- 提供流程回溯和问题定位功能
- 记录数据一致性检查和修复的操作日志
```

## API接口设计

### 员工车辆管理接口

#### 1. 员工车辆申请接口

**接口地址：** `POST /admin-api/vehicle/employee/create`

**请求参数：**

```java
@Data
@ApiModel("员工车辆申请创建 Request VO")
public class VehicleEmployeeCreateReqVO {

    @ApiModelProperty(value = "车牌号", required = true, example = "粤B12345")
    @NotBlank(message = "车牌号不能为空")
    @Pattern(regexp = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$",
             message = "车牌号格式不正确")
    private String vehiclePlate;

    @ApiModelProperty(value = "车辆品牌", example = "丰田")
    private String vehicleBrand;

    @ApiModelProperty(value = "车辆型号", example = "凯美瑞")
    private String vehicleModel;

    @ApiModelProperty(value = "车辆颜色", example = "白色")
    private String vehicleColor;

    @ApiModelProperty(value = "车辆类型", required = true, example = "1", notes = "1-轿车 2-SUV 3-MPV 4-货车 5-摩托车 6-电动车")
    @NotNull(message = "车辆类型不能为空")
    @InEnum(VehicleTypeEnum.class, message = "车辆类型必须是 {value}")
    private Integer vehicleType;

    @ApiModelProperty(value = "燃料类型", example = "1", notes = "1-汽油 2-柴油 3-电动 4-混合动力")
    @InEnum(FuelTypeEnum.class, message = "燃料类型必须是 {value}")
    private Integer fuelType;

    @ApiModelProperty(value = "发动机号", example = "123456789")
    private String engineNo;

    @ApiModelProperty(value = "车架号", example = "ABCDEFG123456789")
    private String vinCode;

    @ApiModelProperty(value = "车辆照片URL", example = "https://example.com/vehicle.jpg")
    private String vehiclePhoto;

    @ApiModelProperty(value = "行驶证照片URL", example = "https://example.com/license.jpg")
    private String drivingLicensePhoto;

    @ApiModelProperty(value = "保险单照片URL", example = "https://example.com/insurance.jpg")
    private String insurancePhoto;

    @ApiModelProperty(value = "申请原因", required = true, example = "日常通勤需要")
    @NotBlank(message = "申请原因不能为空")
    @Length(max = 200, message = "申请原因长度不能超过200个字符")
    private String applicationReason;

}
```

**响应参数：**

```java
@Data
@ApiModel("员工车辆申请创建 Response VO")
public class VehicleEmployeeCreateRespVO {

    @ApiModelProperty(value = "申请ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "申请单号", example = "VE20250111001")
    private String applicationNo;

    @ApiModelProperty(value = "流程实例ID", example = "12345")
    private String processInstanceId;

    @ApiModelProperty(value = "申请状态", example = "1", notes = "1-待审批 2-已审批 3-已驳回")
    private Integer status;

    @ApiModelProperty(value = "申请时间", example = "2025-01-11 10:30:00")
    private LocalDateTime applicationTime;

}
```

**接口实现：**

```java
@RestController
@RequestMapping("/admin-api/vehicle/employee")
@Tag(name = "管理后台 - 员工车辆")
@Validated
public class VehicleEmployeeController {

    @Resource
    private VehicleEmployeeService vehicleEmployeeService;

    @PostMapping("/create")
    @Operation(summary = "创建员工车辆申请")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:create')")
    public CommonResult<VehicleEmployeeCreateRespVO> createVehicleEmployee(@Valid @RequestBody VehicleEmployeeCreateReqVO createReqVO) {
        Long id = vehicleEmployeeService.createVehicleEmployee(createReqVO);
        return success(vehicleEmployeeService.getVehicleEmployeeCreateResp(id));
    }

    @PutMapping("/update")
    @Operation(summary = "更新员工车辆申请")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:update')")
    public CommonResult<Boolean> updateVehicleEmployee(@Valid @RequestBody VehicleEmployeeUpdateReqVO updateReqVO) {
        vehicleEmployeeService.updateVehicleEmployee(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除员工车辆申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:delete')")
    public CommonResult<Boolean> deleteVehicleEmployee(@RequestParam("id") Long id) {
        vehicleEmployeeService.deleteVehicleEmployee(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得员工车辆申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:query')")
    public CommonResult<VehicleEmployeeRespVO> getVehicleEmployee(@RequestParam("id") Long id) {
        VehicleEmployeeDO vehicleEmployee = vehicleEmployeeService.getVehicleEmployee(id);
        return success(VehicleEmployeeConvert.INSTANCE.convert(vehicleEmployee));
    }

    @GetMapping("/page")
    @Operation(summary = "获得员工车辆申请分页")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:query')")
    public CommonResult<PageResult<VehicleEmployeeRespVO>> getVehicleEmployeePage(@Valid VehicleEmployeePageReqVO pageReqVO) {
        PageResult<VehicleEmployeeDO> pageResult = vehicleEmployeeService.getVehicleEmployeePage(pageReqVO);
        return success(VehicleEmployeeConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/approve")
    @Operation(summary = "审批员工车辆申请")
    @PreAuthorize("@ss.hasPermission('vehicle:employee:approve')")
    public CommonResult<Boolean> approveVehicleEmployee(@Valid @RequestBody VehicleEmployeeApproveReqVO approveReqVO) {
        vehicleEmployeeService.approveVehicleEmployee(approveReqVO);
        return success(true);
    }

}
```

#### 2. 车辆申请管理接口

**接口地址：** `POST /admin-api/vehicle/application/create`

**请求参数：**

```java
@Data
@ApiModel("车辆申请创建 Request VO")
public class VehicleApplicationCreateReqVO {

    @ApiModelProperty(value = "申请类型", required = true, example = "1", notes = "1-公车使用申请 2-维保申请 3-加油申请 4-年检申请 5-保险申请")
    @NotNull(message = "申请类型不能为空")
    @InEnum(ApplicationTypeEnum.class, message = "申请类型必须是 {value}")
    private Integer applicationType;

    // 公车使用申请字段
    @ApiModelProperty(value = "申请车辆ID", example = "1")
    private Long vehicleId;

    @ApiModelProperty(value = "用车目的", example = "客户拜访")
    @Length(max = 200, message = "用车目的长度不能超过200个字符")
    private String usePurpose;

    @ApiModelProperty(value = "用车开始时间", example = "2025-01-11 09:00:00")
    @Future(message = "用车开始时间必须是未来时间")
    private LocalDateTime useStartTime;

    @ApiModelProperty(value = "用车结束时间", example = "2025-01-11 18:00:00")
    @Future(message = "用车结束时间必须是未来时间")
    private LocalDateTime useEndTime;

    @ApiModelProperty(value = "目的地", example = "深圳市南山区")
    @Length(max = 200, message = "目的地长度不能超过200个字符")
    private String destination;

    @ApiModelProperty(value = "乘车人数", example = "3")
    @Min(value = 1, message = "乘车人数不能少于1人")
    @Max(value = 20, message = "乘车人数不能超过20人")
    private Integer passengerCount;

    @ApiModelProperty(value = "乘车人员名单", example = "张三,李四,王五")
    private String passengerList;

    @ApiModelProperty(value = "预计里程", example = "100")
    @Min(value = 1, message = "预计里程不能少于1公里")
    private Integer estimatedMileage;

    @ApiModelProperty(value = "指定司机ID", example = "1")
    private Long driverId;

    // 维保申请字段
    @ApiModelProperty(value = "维保类型", example = "1", notes = "1-定期保养 2-故障维修 3-事故维修 4-改装升级")
    private Integer maintenanceType;

    @ApiModelProperty(value = "维保原因", example = "车辆异响需要检修")
    @Length(max = 500, message = "维保原因长度不能超过500个字符")
    private String maintenanceReason;

    @ApiModelProperty(value = "维保项目", example = "更换机油,检查刹车片")
    private String maintenanceItems;

    @ApiModelProperty(value = "预估费用", example = "1500.00")
    @DecimalMin(value = "0", message = "预估费用不能为负数")
    private BigDecimal estimatedCost;

    @ApiModelProperty(value = "首选维修厂", example = "4S店")
    @Length(max = 100, message = "首选维修厂长度不能超过100个字符")
    private String preferredGarage;

    @ApiModelProperty(value = "紧急程度", example = "2", notes = "1-紧急 2-普通 3-不急")
    @InEnum(UrgencyLevelEnum.class, message = "紧急程度必须是 {value}")
    private Integer urgencyLevel;

    // 加油申请字段
    @ApiModelProperty(value = "燃料类型", example = "1", notes = "1-汽油 2-柴油")
    private Integer fuelType;

    @ApiModelProperty(value = "加油量", example = "50.5")
    @DecimalMin(value = "0.1", message = "加油量不能少于0.1升")
    @DecimalMax(value = "200", message = "加油量不能超过200升")
    private BigDecimal fuelAmount;

    @ApiModelProperty(value = "加油费用", example = "350.00")
    @DecimalMin(value = "0", message = "加油费用不能为负数")
    private BigDecimal fuelCost;

    @ApiModelProperty(value = "加油站", example = "中石化加油站")
    @Length(max = 100, message = "加油站长度不能超过100个字符")
    private String gasStation;

    @ApiModelProperty(value = "当前里程", example = "15000")
    @Min(value = 0, message = "当前里程不能为负数")
    private Integer currentMileage;

}
```

## 业务流程设计

### 核心业务流程

#### 1. 员工车辆申请流程

```mermaid
graph TD
    A[员工提交车辆申请] --> B{基础信息验证}
    B -->|验证失败| C[返回错误信息]
    B -->|验证通过| D[生成申请单号]
    D --> E[启动审批流程]
    E --> F[部门主管审批]
    F -->|驳回| G[发送驳回通知]
    F -->|通过| H[车管部审批]
    H -->|驳回| G
    H -->|通过| I[生成车辆通行证]
    I --> J[发送审批通过通知]
    J --> K[车辆信息生效]

    G --> L[流程结束]
    K --> L
    C --> L
```

**流程详细说明：**

**步骤1：员工提交申请**
- 员工登录系统，填写车辆基本信息
- 上传车辆照片、行驶证、保险单等必要材料
- 系统自动获取员工基本信息（姓名、部门、工号等）
- 验证车牌号格式和唯一性
- 生成申请单号（格式：VE + 日期 + 序号）

**步骤2：部门主管审批**
- 系统自动分配给员工所在部门的主管
- 主管可以查看申请详情和附件材料
- 审批选项：通过、驳回、退回修改
- 审批时限：48小时，超时自动升级

**步骤3：车管部审批**
- 车管部负责最终审核
- 检查车辆信息的真实性和合规性
- 确认停车位资源和通行权限
- 审批时限：24小时

**步骤4：生成通行证**
- 审批通过后自动生成电子通行证
- 包含二维码、有效期、停车区域等信息
- 发送通知给申请人和相关部门

#### 2. 公车使用申请流程

```mermaid
graph TD
    A[员工提交用车申请] --> B{申请信息验证}
    B -->|验证失败| C[返回错误信息]
    B -->|验证通过| D[检查车辆可用性]
    D -->|车辆不可用| E[推荐其他车辆]
    D -->|车辆可用| F[部门主管审批]
    F -->|驳回| G[发送驳回通知]
    F -->|通过| H{是否需要上级审批}
    H -->|是| I[上级领导审批]
    H -->|否| J[车管部分配]
    I -->|驳回| G
    I -->|通过| J
    J --> K[分配司机]
    K --> L[生成用车单]
    L --> M[发送确认通知]
    M --> N[开始用车]
    N --> O[结束用车]
    O --> P[费用结算]
    P --> Q[流程完成]

    C --> R[流程结束]
    E --> R
    G --> R
    Q --> R
```

**流程详细说明：**

**步骤1：申请提交**
- 选择用车类型和具体车辆
- 填写用车目的、时间、目的地等信息
- 系统检查时间冲突和车辆可用性
- 自动计算预估费用

**步骤2：审批流程**
- 部门主管审批（必须）
- 根据用车时长和费用确定是否需要上级审批
- 超过3天或费用超过1000元需要上级审批

**步骤3：车辆分配**
- 车管部确认车辆分配
- 根据用车需求分配合适的司机
- 生成用车单和行程安排

**步骤4：用车执行**
- 司机按时到达指定地点
- 记录实际用车时间和里程
- 处理用车过程中的异常情况

**步骤5：费用结算**
- 自动计算实际费用（燃油、过路费等）
- 生成费用明细和报销单
- 更新车辆使用记录

#### 3. 车辆维保申请流程

```mermaid
graph TD
    A[提交维保申请] --> B{申请类型判断}
    B -->|定期保养| C[车管部审批]
    B -->|故障维修| D[紧急程度评估]
    B -->|事故维修| E[安全部介入]

    D -->|一般故障| C
    D -->|紧急故障| F[快速审批通道]

    C -->|通过| G[选择维修厂]
    F -->|通过| G
    E --> H[事故调查]
    H --> I[责任认定]
    I --> G

    G --> J[预约维修时间]
    J --> K[车辆送修]
    K --> L[维修进度跟踪]
    L --> M[质量验收]
    M -->|验收通过| N[费用结算]
    M -->|验收不通过| O[返工处理]
    O --> L
    N --> P[更新车辆档案]
    P --> Q[流程完成]
```

### 异常场景处理决策树

**车辆申请异常处理决策树**

```mermaid
graph TD
    A[车辆申请异常] --> B{异常类型判断}

    B -->|信息不完整| C[信息完整性检查]
    B -->|车牌重复| D[车牌冲突处理]
    B -->|材料不清晰| E[材料质量检查]
    B -->|超时未审批| F[超时处理]
    B -->|系统故障| G[技术故障处理]

    C --> C1{缺失字段类型}
    C1 -->|必填字段缺失| C2[要求补充信息]
    C1 -->|附件缺失| C3[要求上传附件]
    C1 -->|格式错误| C4[格式校验提示]

    D --> D1{车牌状态检查}
    D1 -->|已注册有效| D2[拒绝申请+说明原因]
    D1 -->|已注册无效| D3[提示更新原记录]
    D1 -->|正在申请中| D4[提示等待审批结果]

    E --> E1{材料类型}
    E1 -->|行驶证不清晰| E2[要求重新上传]
    E1 -->|车辆照片模糊| E3[要求补充照片]
    E1 -->|保险单过期| E4[要求更新保险]

    F --> F1{超时阶段}
    F1 -->|部门审批超时| F2[自动升级给上级]
    F1 -->|车管审批超时| F3[发送催办通知]
    F1 -->|申请人超时| F4[自动取消申请]

    G --> G1{故障类型}
    G1 -->|数据库故障| G2[启用备用数据源]
    G1 -->|流程引擎故障| G3[人工审批模式]
    G1 -->|文件服务故障| G4[本地存储模式]
```

### 角色权限矩阵详细说明

| 角色 | 车辆申请 | 申请审批 | 车辆分配 | 司机管理 | 维保管理 | 费用管理 | 异常处理 | 数据查看 | 系统配置 |
|------|----------|----------|----------|----------|----------|----------|----------|----------|----------|
| **普通员工** | ✓ | ✗ | ✗ | ✗ | ✗ | 个人费用 | 申请修改 | 个人申请 | ✗ |
| **部门主管** | ✓ | 部门申请 | ✗ | ✗ | ✗ | 部门费用 | 部门异常 | 部门数据 | ✗ |
| **车管员** | ✓ | 所有申请 | ✓ | ✓ | ✓ | 所有费用 | 车辆异常 | 车辆数据 | 车辆配置 |
| **财务人员** | ✗ | 费用审批 | ✗ | ✗ | 费用审核 | ✓ | 费用异常 | 费用数据 | 费用配置 |
| **安全员** | ✗ | 安全审批 | ✗ | 资质审核 | 安全检查 | ✗ | 安全异常 | 安全数据 | 安全配置 |
| **司机** | ✗ | ✗ | 接受分配 | 个人信息 | 报告故障 | 费用录入 | 现场处理 | 个人任务 | ✗ |
| **系统管理员** | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |

**权限细化说明：**

**普通员工权限：**
- 只能申请自己的车辆登记和用车需求
- 可以查看自己的申请状态和历史记录
- 可以在"待审批"状态下修改申请信息
- 可以取消未开始审批的申请
- 可以查看个人相关的费用记录

**部门主管权限：**
- 可以审批本部门员工的车辆申请
- 可以查看本部门所有车辆相关数据
- 可以处理本部门的车辆异常情况
- 可以代为提交部门用车申请
- 可以设置部门车辆管理规则

**车管员权限：**
- 拥有最高的车辆业务权限
- 可以审批所有类型的车辆申请
- 可以分配车辆和司机资源
- 可以管理车辆维保和费用
- 可以处理所有车辆相关异常

**财务人员权限：**
- 专门负责费用相关的审批和管理
- 可以审核维保费用和报销申请
- 可以查看所有费用数据和统计报表
- 可以设置费用标准和审批规则

### 业务规则详细说明

**1. 车辆申请规则**

```
车辆申请验证算法：
IF 车牌号格式不正确 THEN
    返回错误："车牌号格式不正确"
END IF

IF EXISTS(相同车牌号 WHERE 状态 IN [待审批, 已审批, 有效]) THEN
    返回错误："该车牌号已存在有效记录"
END IF

IF 申请人已有有效车辆数量 >= 2 THEN
    返回警告："每人最多只能申请2辆车"
END IF

IF 车辆类型 == 货车 AND 申请人职级 < 经理级 THEN
    返回警告："货车申请需要经理级以上职级"
END IF
```

**2. 用车申请规则**

```
用车申请验证算法：
IF 用车开始时间 < 当前时间 + 2小时 THEN
    返回错误："用车时间不能早于当前时间2小时"
END IF

IF 用车结束时间 <= 用车开始时间 THEN
    返回错误："用车结束时间必须晚于开始时间"
END IF

IF 用车时长 > 7天 THEN
    返回错误："单次用车时长不能超过7天"
END IF

IF EXISTS(车辆冲突 WHERE 车辆ID = 申请车辆ID AND 时间重叠) THEN
    返回错误："该时间段车辆已被预约"
END IF

// 费用审批规则
IF 预估费用 > 1000 OR 用车天数 > 3 THEN
    需要上级审批 = TRUE
END IF
```

**3. 维保申请规则**

```
维保申请验证算法：
IF 维保类型 == 定期保养 THEN
    IF 距离上次保养时间 < 3个月 AND 距离上次保养里程 < 5000公里 THEN
        返回警告："保养间隔时间过短"
    END IF
END IF

IF 维保类型 == 故障维修 AND 紧急程度 == 紧急 THEN
    审批时限 = 4小时
    通知方式 = 电话 + 短信
END IF

IF 预估费用 > 5000 THEN
    需要财务审批 = TRUE
END IF

IF 维保类型 == 事故维修 THEN
    需要安全部审批 = TRUE
    需要事故调查 = TRUE
END IF
```

### 技术实现深度细化

#### A. 关键业务逻辑算法

**1. 车辆调度算法**

```java
/**
 * 智能车辆调度服务
 */
@Service
@Slf4j
public class VehicleScheduleService {

    @Resource
    private VehicleCompanyMapper vehicleCompanyMapper;

    @Resource
    private VehicleApplicationMapper applicationMapper;

    /**
     * 智能车辆分配算法
     */
    public VehicleScheduleResult scheduleVehicle(VehicleApplicationDO application) {
        try {
            // 1. 获取可用车辆列表
            List<VehicleCompanyDO> availableVehicles = getAvailableVehicles(
                application.getUseStartTime(),
                application.getUseEndTime(),
                application.getPassengerCount()
            );

            if (availableVehicles.isEmpty()) {
                return VehicleScheduleResult.noVehicleAvailable();
            }

            // 2. 车辆匹配度评分
            List<VehicleScoreResult> scoredVehicles = scoreVehicles(availableVehicles, application);

            // 3. 选择最优车辆
            VehicleScoreResult bestVehicle = selectBestVehicle(scoredVehicles);

            // 4. 分配司机
            DriverAssignResult driverResult = assignDriver(bestVehicle.getVehicle(), application);

            // 5. 创建调度记录
            VehicleScheduleRecord scheduleRecord = createScheduleRecord(bestVehicle, driverResult, application);

            return VehicleScheduleResult.success(scheduleRecord);

        } catch (Exception e) {
            log.error("车辆调度失败", e);
            return VehicleScheduleResult.error("车辆调度失败：" + e.getMessage());
        }
    }

    /**
     * 车辆匹配度评分算法
     */
    private List<VehicleScoreResult> scoreVehicles(List<VehicleCompanyDO> vehicles, VehicleApplicationDO application) {
        List<VehicleScoreResult> results = new ArrayList<>();

        for (VehicleCompanyDO vehicle : vehicles) {
            double score = calculateVehicleScore(vehicle, application);
            results.add(new VehicleScoreResult(vehicle, score));
        }

        // 按评分降序排列
        results.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
        return results;
    }

    /**
     * 车辆评分计算
     */
    private double calculateVehicleScore(VehicleCompanyDO vehicle, VehicleApplicationDO application) {
        double score = 0.0;

        // 1. 座位数匹配度 (权重: 30%)
        double seatScore = calculateSeatScore(vehicle.getSeatingCapacity(), application.getPassengerCount());
        score += seatScore * 0.3;

        // 2. 燃油经济性 (权重: 25%)
        double fuelScore = calculateFuelScore(vehicle.getFuelConsumption());
        score += fuelScore * 0.25;

        // 3. 车辆状态 (权重: 20%)
        double statusScore = calculateStatusScore(vehicle);
        score += statusScore * 0.2;

        // 4. 使用频率 (权重: 15%)
        double usageScore = calculateUsageScore(vehicle.getId());
        score += usageScore * 0.15;

        // 5. 维保状态 (权重: 10%)
        double maintenanceScore = calculateMaintenanceScore(vehicle);
        score += maintenanceScore * 0.1;

        return score;
    }

    /**
     * 座位数匹配度评分
     */
    private double calculateSeatScore(Integer vehicleSeats, Integer requiredSeats) {
        if (vehicleSeats == null || requiredSeats == null) {
            return 0.5; // 默认中等评分
        }

        if (vehicleSeats < requiredSeats) {
            return 0.0; // 座位不足，不可用
        }

        if (vehicleSeats.equals(requiredSeats)) {
            return 1.0; // 完全匹配
        }

        // 座位过多会降低评分
        double excess = (double) (vehicleSeats - requiredSeats) / requiredSeats;
        return Math.max(0.3, 1.0 - excess * 0.5);
    }

    /**
     * 燃油经济性评分
     */
    private double calculateFuelScore(BigDecimal fuelConsumption) {
        if (fuelConsumption == null) {
            return 0.5;
        }

        double consumption = fuelConsumption.doubleValue();

        // 油耗越低评分越高
        if (consumption <= 6.0) {
            return 1.0; // 优秀
        } else if (consumption <= 8.0) {
            return 0.8; // 良好
        } else if (consumption <= 10.0) {
            return 0.6; // 一般
        } else if (consumption <= 12.0) {
            return 0.4; // 较差
        } else {
            return 0.2; // 很差
        }
    }

    /**
     * 司机分配算法
     */
    private DriverAssignResult assignDriver(VehicleCompanyDO vehicle, VehicleApplicationDO application) {
        // 1. 获取可用司机列表
        List<DriverDO> availableDrivers = getAvailableDrivers(
            application.getUseStartTime(),
            application.getUseEndTime()
        );

        // 2. 司机匹配度评分
        List<DriverScoreResult> scoredDrivers = scoreDrivers(availableDrivers, vehicle, application);

        // 3. 选择最优司机
        if (scoredDrivers.isEmpty()) {
            return DriverAssignResult.noDriverAvailable();
        }

        DriverScoreResult bestDriver = scoredDrivers.get(0);
        return DriverAssignResult.success(bestDriver.getDriver());
    }
}
```

**2. 车辆状态机算法**

```java
/**
 * 车辆状态机服务
 */
@Service
public class VehicleStatusMachine {

    // 员工车辆状态转换映射表
    private static final Map<Integer, Set<Integer>> EMPLOYEE_VEHICLE_TRANSITIONS = new HashMap<>();

    // 公司车辆状态转换映射表
    private static final Map<Integer, Set<Integer>> COMPANY_VEHICLE_TRANSITIONS = new HashMap<>();

    // 申请状态转换映射表
    private static final Map<Integer, Set<Integer>> APPLICATION_TRANSITIONS = new HashMap<>();

    static {
        // 员工车辆状态转换
        EMPLOYEE_VEHICLE_TRANSITIONS.put(VehicleStatusEnum.PENDING_APPROVAL.getStatus(),
            Set.of(VehicleStatusEnum.APPROVED.getStatus(), VehicleStatusEnum.REJECTED.getStatus()));
        EMPLOYEE_VEHICLE_TRANSITIONS.put(VehicleStatusEnum.APPROVED.getStatus(),
            Set.of(VehicleStatusEnum.ACTIVE.getStatus(), VehicleStatusEnum.REJECTED.getStatus()));
        EMPLOYEE_VEHICLE_TRANSITIONS.put(VehicleStatusEnum.ACTIVE.getStatus(),
            Set.of(VehicleStatusEnum.INACTIVE.getStatus(), VehicleStatusEnum.CANCELLED.getStatus()));

        // 公司车辆状态转换
        COMPANY_VEHICLE_TRANSITIONS.put(CompanyVehicleStatusEnum.NORMAL.getStatus(),
            Set.of(CompanyVehicleStatusEnum.MAINTENANCE.getStatus(), CompanyVehicleStatusEnum.DISABLED.getStatus()));
        COMPANY_VEHICLE_TRANSITIONS.put(CompanyVehicleStatusEnum.MAINTENANCE.getStatus(),
            Set.of(CompanyVehicleStatusEnum.NORMAL.getStatus(), CompanyVehicleStatusEnum.SCRAPPED.getStatus()));

        // 申请状态转换
        APPLICATION_TRANSITIONS.put(ApplicationStatusEnum.PENDING_APPROVAL.getStatus(),
            Set.of(ApplicationStatusEnum.APPROVED.getStatus(), ApplicationStatusEnum.REJECTED.getStatus()));
        APPLICATION_TRANSITIONS.put(ApplicationStatusEnum.APPROVED.getStatus(),
            Set.of(ApplicationStatusEnum.IN_USE.getStatus(), ApplicationStatusEnum.CANCELLED.getStatus()));
        APPLICATION_TRANSITIONS.put(ApplicationStatusEnum.IN_USE.getStatus(),
            Set.of(ApplicationStatusEnum.COMPLETED.getStatus()));
    }

    /**
     * 状态转换验证
     */
    public boolean canTransition(VehicleType vehicleType, Integer fromStatus, Integer toStatus) {
        Map<Integer, Set<Integer>> transitions = getTransitionMap(vehicleType);
        Set<Integer> allowedTransitions = transitions.get(fromStatus);
        return allowedTransitions != null && allowedTransitions.contains(toStatus);
    }

    /**
     * 执行状态转换
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeStatusTransition(VehicleType vehicleType, Long targetId, Integer toStatus,
                                      String reason, Long operatorId) {
        // 1. 获取当前状态
        Integer fromStatus = getCurrentStatus(vehicleType, targetId);

        // 2. 验证状态转换合法性
        if (!canTransition(vehicleType, fromStatus, toStatus)) {
            throw new ServiceException("状态转换不合法：从" + fromStatus + "到" + toStatus);
        }

        // 3. 执行状态转换前的业务逻辑
        executePreTransitionLogic(vehicleType, targetId, toStatus);

        // 4. 更新状态
        updateStatus(vehicleType, targetId, toStatus);

        // 5. 记录状态变更历史
        recordStatusHistory(vehicleType, targetId, fromStatus, toStatus, reason, operatorId);

        // 6. 执行状态转换后的业务逻辑
        executePostTransitionLogic(vehicleType, targetId, fromStatus, toStatus);

        // 7. 发送状态变更通知
        sendStatusChangeNotification(vehicleType, targetId, fromStatus, toStatus);
    }

    /**
     * 状态转换前置逻辑
     */
    private void executePreTransitionLogic(VehicleType vehicleType, Long targetId, Integer toStatus) {
        switch (vehicleType) {
            case EMPLOYEE_VEHICLE:
                handleEmployeeVehiclePreTransition(targetId, toStatus);
                break;
            case COMPANY_VEHICLE:
                handleCompanyVehiclePreTransition(targetId, toStatus);
                break;
            case APPLICATION:
                handleApplicationPreTransition(targetId, toStatus);
                break;
        }
    }

    private void handleEmployeeVehiclePreTransition(Long vehicleId, Integer toStatus) {
        if (toStatus.equals(VehicleStatusEnum.ACTIVE.getStatus())) {
            // 生成车辆通行证
            generateVehiclePass(vehicleId);
        } else if (toStatus.equals(VehicleStatusEnum.CANCELLED.getStatus())) {
            // 注销车辆通行证
            revokeVehiclePass(vehicleId);
        }
    }

    private void handleApplicationPreTransition(Long applicationId, Integer toStatus) {
        if (toStatus.equals(ApplicationStatusEnum.APPROVED.getStatus())) {
            // 分配车辆和司机
            scheduleVehicleAndDriver(applicationId);
        } else if (toStatus.equals(ApplicationStatusEnum.IN_USE.getStatus())) {
            // 开始计费
            startBilling(applicationId);
        } else if (toStatus.equals(ApplicationStatusEnum.COMPLETED.getStatus())) {
            // 结束计费
            endBilling(applicationId);
        }
    }
}
```

#### B. 数据校验规则详细实现

**1. 统一数据校验框架**

```java
/**
 * 车辆管理数据校验器
 */
@Component
public class VehicleDataValidator {

    @Resource
    private VehicleBlacklistService blacklistService;

    @Resource
    private VehicleCompanyMapper vehicleCompanyMapper;

    /**
     * 员工车辆申请数据校验
     */
    public ValidationResult validateEmployeeVehicle(VehicleEmployeeCreateReqVO reqVO) {
        ValidationResult result = new ValidationResult();

        // 1. 基础字段校验
        validateBasicFields(reqVO, result);

        // 2. 业务规则校验
        validateBusinessRules(reqVO, result);

        // 3. 安全校验
        validateSecurity(reqVO, result);

        // 4. 数据一致性校验
        validateDataConsistency(reqVO, result);

        return result;
    }

    /**
     * 基础字段校验
     */
    private void validateBasicFields(VehicleEmployeeCreateReqVO reqVO, ValidationResult result) {
        // 车牌号校验
        if (StringUtils.isBlank(reqVO.getVehiclePlate())) {
            result.addError("vehiclePlate", "车牌号不能为空");
        } else if (!isValidVehiclePlate(reqVO.getVehiclePlate())) {
            result.addError("vehiclePlate", "车牌号格式不正确");
        }

        // 发动机号校验
        if (StringUtils.isNotBlank(reqVO.getEngineNo())) {
            if (!isValidEngineNo(reqVO.getEngineNo())) {
                result.addError("engineNo", "发动机号格式不正确");
            }
        }

        // 车架号校验
        if (StringUtils.isNotBlank(reqVO.getVinCode())) {
            if (!isValidVinCode(reqVO.getVinCode())) {
                result.addError("vinCode", "车架号格式不正确");
            }
        }

        // 申请原因长度校验
        if (StringUtils.isNotBlank(reqVO.getApplicationReason())) {
            if (reqVO.getApplicationReason().length() > 200) {
                result.addError("applicationReason", "申请原因长度不能超过200个字符");
            }
        }
    }

    /**
     * 业务规则校验
     */
    private void validateBusinessRules(VehicleEmployeeCreateReqVO reqVO, ValidationResult result) {
        // 检查车牌号是否已存在
        if (isVehiclePlateExists(reqVO.getVehiclePlate())) {
            result.addError("vehiclePlate", "该车牌号已存在");
        }

        // 检查用户车辆数量限制
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        int userVehicleCount = getUserVehicleCount(userId);
        if (userVehicleCount >= 2) {
            result.addError("vehicleLimit", "每人最多只能申请2辆车");
        }

        // 检查车辆类型权限
        if (reqVO.getVehicleType() != null && reqVO.getVehicleType() == VehicleTypeEnum.TRUCK.getType()) {
            if (!hasPermissionForTruck(userId)) {
                result.addError("vehicleType", "货车申请需要经理级以上职级");
            }
        }
    }

    /**
     * 安全校验
     */
    private void validateSecurity(VehicleEmployeeCreateReqVO reqVO, ValidationResult result) {
        // 黑名单校验
        if (blacklistService.isVehicleInBlacklist(reqVO.getVehiclePlate())) {
            result.addError("security", "该车牌号被列入黑名单，禁止申请");
        }

        if (StringUtils.isNotBlank(reqVO.getEngineNo()) &&
            blacklistService.isEngineInBlacklist(reqVO.getEngineNo())) {
            result.addError("security", "该发动机号被列入黑名单，禁止申请");
        }

        if (StringUtils.isNotBlank(reqVO.getVinCode()) &&
            blacklistService.isVinInBlacklist(reqVO.getVinCode())) {
            result.addError("security", "该车架号被列入黑名单，禁止申请");
        }
    }

    /**
     * 车辆申请数据校验
     */
    public ValidationResult validateVehicleApplication(VehicleApplicationCreateReqVO reqVO) {
        ValidationResult result = new ValidationResult();

        // 根据申请类型进行不同的校验
        switch (reqVO.getApplicationType()) {
            case 1: // 公车使用申请
                validateVehicleUsageApplication(reqVO, result);
                break;
            case 2: // 维保申请
                validateMaintenanceApplication(reqVO, result);
                break;
            case 3: // 加油申请
                validateFuelApplication(reqVO, result);
                break;
            default:
                result.addError("applicationType", "不支持的申请类型");
        }

        return result;
    }

    /**
     * 公车使用申请校验
     */
    private void validateVehicleUsageApplication(VehicleApplicationCreateReqVO reqVO, ValidationResult result) {
        // 时间校验
        LocalDateTime now = LocalDateTime.now();
        if (reqVO.getUseStartTime() != null) {
            if (reqVO.getUseStartTime().isBefore(now.plusHours(2))) {
                result.addError("useStartTime", "用车时间不能早于当前时间2小时");
            }
        }

        if (reqVO.getUseEndTime() != null && reqVO.getUseStartTime() != null) {
            if (reqVO.getUseEndTime().isBefore(reqVO.getUseStartTime())) {
                result.addError("useEndTime", "用车结束时间必须晚于开始时间");
            }

            long hours = Duration.between(reqVO.getUseStartTime(), reqVO.getUseEndTime()).toHours();
            if (hours > 168) { // 7天
                result.addError("useTime", "单次用车时长不能超过7天");
            }
        }

        // 车辆可用性校验
        if (reqVO.getVehicleId() != null) {
            if (!isVehicleAvailable(reqVO.getVehicleId(), reqVO.getUseStartTime(), reqVO.getUseEndTime())) {
                result.addError("vehicleId", "该时间段车辆已被预约");
            }
        }

        // 乘车人数校验
        if (reqVO.getPassengerCount() != null && reqVO.getVehicleId() != null) {
            VehicleCompanyDO vehicle = vehicleCompanyMapper.selectById(reqVO.getVehicleId());
            if (vehicle != null && vehicle.getSeatingCapacity() != null) {
                if (reqVO.getPassengerCount() > vehicle.getSeatingCapacity()) {
                    result.addError("passengerCount", "乘车人数超过车辆座位数");
                }
            }
        }
    }

    /**
     * 正则表达式校验方法
     */
    private boolean isValidVehiclePlate(String plate) {
        // 支持新能源车牌和普通车牌
        return plate.matches("^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$");
    }

    private boolean isValidEngineNo(String engineNo) {
        // 发动机号通常为字母数字组合，长度6-20位
        return engineNo.matches("^[A-Z0-9]{6,20}$");
    }

    private boolean isValidVinCode(String vinCode) {
        // 车架号为17位字母数字组合，不包含I、O、Q
        return vinCode.matches("^[A-HJ-NPR-Z0-9]{17}$");
    }
}
```

#### C. 缓存策略详细设计

**1. 多级缓存架构**

```java
/**
 * 车辆管理缓存服务
 */
@Service
public class VehicleCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private CaffeineCache localCache;

    // 缓存键前缀
    private static final String CACHE_PREFIX = "vehicle:";
    private static final String EMPLOYEE_VEHICLE_CACHE_KEY = CACHE_PREFIX + "employee:";
    private static final String COMPANY_VEHICLE_CACHE_KEY = CACHE_PREFIX + "company:";
    private static final String APPLICATION_CACHE_KEY = CACHE_PREFIX + "application:";
    private static final String SCHEDULE_CACHE_KEY = CACHE_PREFIX + "schedule:";

    // 缓存过期时间
    private static final Duration VEHICLE_CACHE_TTL = Duration.ofMinutes(30);
    private static final Duration APPLICATION_CACHE_TTL = Duration.ofMinutes(15);
    private static final Duration SCHEDULE_CACHE_TTL = Duration.ofMinutes(5);

    /**
     * 缓存员工车辆信息
     */
    public void cacheEmployeeVehicle(VehicleEmployeeDO vehicle) {
        String key = EMPLOYEE_VEHICLE_CACHE_KEY + vehicle.getId();

        // L1缓存：本地缓存
        localCache.put(key, vehicle, VEHICLE_CACHE_TTL);

        // L2缓存：Redis缓存
        redisTemplate.opsForValue().set(key, vehicle, VEHICLE_CACHE_TTL);

        // 缓存用户车辆列表
        cacheUserVehicleList(vehicle.getUserId());
    }

    /**
     * 获取缓存的员工车辆信息
     */
    public VehicleEmployeeDO getCachedEmployeeVehicle(Long vehicleId) {
        String key = EMPLOYEE_VEHICLE_CACHE_KEY + vehicleId;

        // 先查本地缓存
        VehicleEmployeeDO vehicle = localCache.get(key, VehicleEmployeeDO.class);
        if (vehicle != null) {
            return vehicle;
        }

        // 再查Redis缓存
        vehicle = (VehicleEmployeeDO) redisTemplate.opsForValue().get(key);
        if (vehicle != null) {
            // 回写本地缓存
            localCache.put(key, vehicle, VEHICLE_CACHE_TTL);
            return vehicle;
        }

        return null;
    }

    /**
     * 缓存车辆调度信息
     */
    public void cacheVehicleSchedule(String scheduleKey, VehicleScheduleInfo scheduleInfo) {
        String key = SCHEDULE_CACHE_KEY + scheduleKey;
        redisTemplate.opsForValue().set(key, scheduleInfo, SCHEDULE_CACHE_TTL);
    }

    /**
     * 获取车辆可用性缓存
     */
    public Boolean getCachedVehicleAvailability(Long vehicleId, LocalDateTime startTime, LocalDateTime endTime) {
        String key = SCHEDULE_CACHE_KEY + "availability:" + vehicleId + ":" +
                    startTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")) + ":" +
                    endTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));

        return (Boolean) redisTemplate.opsForValue().get(key);
    }

    /**
     * 缓存车辆可用性
     */
    public void cacheVehicleAvailability(Long vehicleId, LocalDateTime startTime, LocalDateTime endTime, boolean available) {
        String key = SCHEDULE_CACHE_KEY + "availability:" + vehicleId + ":" +
                    startTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")) + ":" +
                    endTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));

        redisTemplate.opsForValue().set(key, available, Duration.ofMinutes(10));
    }

    /**
     * 预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        // 预热活跃车辆缓存
        List<VehicleCompanyDO> activeVehicles = getActiveVehicles();
        activeVehicles.forEach(this::cacheCompanyVehicle);

        // 预热车辆类型配置缓存
        warmUpVehicleTypeCache();

        // 预热司机信息缓存
        warmUpDriverCache();
    }

    /**
     * 清除相关缓存
     */
    public void evictVehicleCache(Long vehicleId, VehicleType vehicleType) {
        String keyPattern;
        switch (vehicleType) {
            case EMPLOYEE_VEHICLE:
                keyPattern = EMPLOYEE_VEHICLE_CACHE_KEY + vehicleId;
                break;
            case COMPANY_VEHICLE:
                keyPattern = COMPANY_VEHICLE_CACHE_KEY + vehicleId;
                break;
            default:
                return;
        }

        localCache.evict(keyPattern);
        redisTemplate.delete(keyPattern);

        // 清除相关的调度缓存
        evictScheduleCache(vehicleId);
    }
}
```

#### D. 性能优化方案

**1. 数据库查询优化**

```sql
-- 创建复合索引优化常用查询
CREATE INDEX idx_vehicle_employee_user_status ON vehicle_employee(user_id, status, tenant_id);
CREATE INDEX idx_vehicle_application_type_time ON vehicle_application(application_type, use_start_time, tenant_id);
CREATE INDEX idx_vehicle_record_plate_time ON vehicle_record(vehicle_plate, record_time, tenant_id);

-- 分区表设计（按月分区）
CREATE TABLE vehicle_record_202501 PARTITION OF vehicle_record
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 查询优化示例
-- 优化前：全表扫描
SELECT * FROM vehicle_application WHERE status = 1 ORDER BY create_time DESC LIMIT 20;

-- 优化后：使用索引
SELECT id, application_no, application_type, applicant_name, status, create_time
FROM vehicle_application
WHERE status = 1 AND tenant_id = ?
ORDER BY create_time DESC
LIMIT 20;
```

**2. 异步处理优化**

```java
/**
 * 车辆异步任务服务
 */
@Service
public class VehicleAsyncService {

    @Async("vehicleTaskExecutor")
    public CompletableFuture<Void> sendVehicleNotificationAsync(Long applicationId, NotificationType type) {
        try {
            vehicleNotificationService.sendNotification(applicationId, type);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("异步发送车辆通知失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Async("vehicleTaskExecutor")
    public CompletableFuture<VehicleScheduleResult> scheduleVehicleAsync(Long applicationId) {
        try {
            VehicleScheduleResult result = vehicleScheduleService.scheduleVehicle(applicationId);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步车辆调度失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @EventListener
    @Async("vehicleTaskExecutor")
    public void handleVehicleStatusChange(VehicleStatusChangeEvent event) {
        // 异步处理车辆状态变更事件
        processVehicleStatusChangeLogic(event);
    }
}
```

### 集成方案深度细化

#### A. 车牌识别系统集成

**1. 车牌识别API封装服务**

```java
/**
 * 车牌识别集成服务
 */
@Service
@Slf4j
public class PlateRecognitionService {

    @Value("${vehicle.plate-recognition.api-url}")
    private String apiUrl;

    @Value("${vehicle.plate-recognition.api-key}")
    private String apiKey;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String RECOGNITION_CACHE_KEY = "plate:recognition:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(5);

    /**
     * 识别车牌号
     */
    public PlateRecognitionResult recognizePlate(String imageUrl) {
        try {
            // 1. 检查缓存
            String cacheKey = RECOGNITION_CACHE_KEY + DigestUtils.md5Hex(imageUrl);
            PlateRecognitionResult cachedResult = (PlateRecognitionResult) redisTemplate.opsForValue().get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }

            // 2. 构建请求参数
            PlateRecognitionRequest request = new PlateRecognitionRequest();
            request.setImageUrl(imageUrl);
            request.setApiKey(apiKey);
            request.setRecognitionType("plate_number");

            // 3. 调用识别API
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<PlateRecognitionRequest> entity = new HttpEntity<>(request, headers);

            PlateRecognitionResponse response = restTemplate.postForObject(
                apiUrl + "/recognize", entity, PlateRecognitionResponse.class);

            // 4. 处理响应结果
            PlateRecognitionResult result = processRecognitionResponse(response);

            // 5. 缓存结果
            if (result.isSuccess()) {
                redisTemplate.opsForValue().set(cacheKey, result, CACHE_TTL);
            }

            return result;

        } catch (Exception e) {
            log.error("车牌识别失败", e);
            return PlateRecognitionResult.error("车牌识别服务异常");
        }
    }

    /**
     * 批量识别车牌
     */
    public List<PlateRecognitionResult> batchRecognizePlates(List<String> imageUrls) {
        List<CompletableFuture<PlateRecognitionResult>> futures = imageUrls.stream()
            .map(imageUrl -> CompletableFuture.supplyAsync(() -> recognizePlate(imageUrl)))
            .collect(Collectors.toList());

        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
    }

    /**
     * 处理识别响应
     */
    private PlateRecognitionResult processRecognitionResponse(PlateRecognitionResponse response) {
        if (response == null || response.getCode() != 200) {
            return PlateRecognitionResult.error("识别服务返回错误");
        }

        PlateRecognitionData data = response.getData();
        if (data == null || CollectionUtils.isEmpty(data.getPlates())) {
            return PlateRecognitionResult.error("未识别到车牌");
        }

        // 选择置信度最高的结果
        PlateInfo bestPlate = data.getPlates().stream()
            .max(Comparator.comparing(PlateInfo::getConfidence))
            .orElse(null);

        if (bestPlate == null || bestPlate.getConfidence() < 0.8) {
            return PlateRecognitionResult.error("识别置信度过低");
        }

        return PlateRecognitionResult.success(bestPlate.getPlateNumber(), bestPlate.getConfidence());
    }

    /**
     * 车牌识别结果验证
     */
    public boolean validateRecognitionResult(String plateNumber, double confidence) {
        // 1. 置信度检查
        if (confidence < 0.8) {
            return false;
        }

        // 2. 车牌格式检查
        if (!isValidPlateFormat(plateNumber)) {
            return false;
        }

        // 3. 黑名单检查
        if (isPlateInBlacklist(plateNumber)) {
            return false;
        }

        return true;
    }
}
```

#### B. 停车场管理系统集成

**1. 停车场API集成服务**

```java
/**
 * 停车场管理系统集成服务
 */
@Service
@Slf4j
public class ParkingSystemService {

    @Value("${vehicle.parking.api-url}")
    private String parkingApiUrl;

    @Value("${vehicle.parking.api-token}")
    private String apiToken;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 查询停车位可用性
     */
    public ParkingAvailabilityResult queryParkingAvailability(String area) {
        try {
            String url = parkingApiUrl + "/parking/availability?area=" + area;

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(apiToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ParkingAvailabilityResponse response = restTemplate.exchange(
                url, HttpMethod.GET, entity, ParkingAvailabilityResponse.class).getBody();

            return processParkingAvailabilityResponse(response);

        } catch (Exception e) {
            log.error("查询停车位可用性失败", e);
            return ParkingAvailabilityResult.error("停车场系统异常");
        }
    }

    /**
     * 预约停车位
     */
    public ParkingReservationResult reserveParkingSpace(ParkingReservationRequest request) {
        try {
            String url = parkingApiUrl + "/parking/reserve";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiToken);
            HttpEntity<ParkingReservationRequest> entity = new HttpEntity<>(request, headers);

            ParkingReservationResponse response = restTemplate.postForObject(
                url, entity, ParkingReservationResponse.class);

            return processParkingReservationResponse(response);

        } catch (Exception e) {
            log.error("预约停车位失败", e);
            return ParkingReservationResult.error("停车位预约失败");
        }
    }

    /**
     * 释放停车位
     */
    public ParkingReleaseResult releaseParkingSpace(String reservationId) {
        try {
            String url = parkingApiUrl + "/parking/release/" + reservationId;

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(apiToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ParkingReleaseResponse response = restTemplate.exchange(
                url, HttpMethod.DELETE, entity, ParkingReleaseResponse.class).getBody();

            return processParkingReleaseResponse(response);

        } catch (Exception e) {
            log.error("释放停车位失败", e);
            return ParkingReleaseResult.error("停车位释放失败");
        }
    }

    /**
     * 同步停车记录
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟同步一次
    public void syncParkingRecords() {
        try {
            // 获取最近的停车记录
            List<ParkingRecord> records = fetchRecentParkingRecords();

            // 同步到本地数据库
            for (ParkingRecord record : records) {
                syncParkingRecordToLocal(record);
            }

        } catch (Exception e) {
            log.error("同步停车记录失败", e);
        }
    }
}
```

### 用户体验深度细化

#### A. 前端交互流程详细设计

**1. 车辆申请页面交互控制器**

```javascript
/**
 * 车辆申请页面交互控制器
 */
class VehicleApplicationController {

    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.formData = {};
        this.validationRules = this.initValidationRules();
        this.autoSaveTimer = null;
    }

    /**
     * 初始化页面
     */
    init() {
        this.initStepIndicator();
        this.initFormValidation();
        this.initAutoSave();
        this.initVehicleSelector();
        this.bindEvents();
    }

    /**
     * 步骤指示器
     */
    initStepIndicator() {
        const steps = [
            { title: '申请类型', icon: 'form' },
            { title: '基本信息', icon: 'info' },
            { title: '详细信息', icon: 'detail' },
            { title: '确认提交', icon: 'check' }
        ];

        this.renderStepIndicator(steps);
    }

    /**
     * 实时表单验证
     */
    initFormValidation() {
        // 车牌号验证
        $('#vehiclePlate').on('blur', (e) => {
            const value = e.target.value.trim().toUpperCase();
            e.target.value = value;
            const result = this.validateVehiclePlate(value);
            this.showFieldValidation('vehiclePlate', result);

            if (result.valid) {
                // 检查车牌号是否已存在
                this.checkVehiclePlateExists(value);
            }
        });

        // 用车时间验证
        $('#useStartTime, #useEndTime').on('change', () => {
            this.validateUsageTime();
        });

        // 乘车人数验证
        $('#passengerCount').on('input', (e) => {
            const count = parseInt(e.target.value);
            const vehicleId = $('#vehicleId').val();

            if (vehicleId && count) {
                this.validatePassengerCount(vehicleId, count);
            }
        });

        // 预估费用计算
        $('#estimatedMileage, #fuelType').on('change', () => {
            this.calculateEstimatedCost();
        });
    }

    /**
     * 智能车辆选择器
     */
    initVehicleSelector() {
        $('#vehicleSelector').on('click', () => {
            this.showVehicleSelectionModal();
        });
    }

    /**
     * 显示车辆选择模态框
     */
    showVehicleSelectionModal() {
        const startTime = $('#useStartTime').val();
        const endTime = $('#useEndTime').val();
        const passengerCount = $('#passengerCount').val();

        if (!startTime || !endTime) {
            this.showWarning('请先选择用车时间');
            return;
        }

        // 获取可用车辆列表
        this.getAvailableVehicles(startTime, endTime, passengerCount)
            .then(vehicles => {
                this.renderVehicleSelectionModal(vehicles);
            })
            .catch(error => {
                this.showError('获取可用车辆失败：' + error.message);
            });
    }

    /**
     * 渲染车辆选择模态框
     */
    renderVehicleSelectionModal(vehicles) {
        const modalHtml = `
            <div class="vehicle-selection-modal">
                <div class="modal-header">
                    <h3>选择车辆</h3>
                    <button class="close-btn" onclick="this.closeVehicleModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="vehicle-filter">
                        <select id="vehicleTypeFilter">
                            <option value="">所有类型</option>
                            <option value="1">商务用车</option>
                            <option value="2">货车</option>
                            <option value="3">观光车</option>
                        </select>
                        <input type="text" id="vehicleSearch" placeholder="搜索车牌号或车型">
                    </div>
                    <div class="vehicle-list">
                        ${this.renderVehicleList(vehicles)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closeVehicleModal()">取消</button>
                    <button class="btn btn-primary" onclick="this.confirmVehicleSelection()">确认选择</button>
                </div>
            </div>
        `;

        $('body').append(modalHtml);
        $('.vehicle-selection-modal').show();
    }

    /**
     * 渲染车辆列表
     */
    renderVehicleList(vehicles) {
        return vehicles.map(vehicle => `
            <div class="vehicle-item" data-vehicle-id="${vehicle.id}">
                <div class="vehicle-info">
                    <div class="vehicle-plate">${vehicle.vehiclePlate}</div>
                    <div class="vehicle-model">${vehicle.vehicleBrand} ${vehicle.vehicleModel}</div>
                    <div class="vehicle-details">
                        <span class="seats">${vehicle.seatingCapacity}座</span>
                        <span class="fuel">${this.getFuelTypeName(vehicle.fuelType)}</span>
                        <span class="status ${this.getStatusClass(vehicle.status)}">${this.getStatusName(vehicle.status)}</span>
                    </div>
                </div>
                <div class="vehicle-score">
                    <div class="score-value">${vehicle.matchScore || 0}</div>
                    <div class="score-label">匹配度</div>
                </div>
                <div class="vehicle-actions">
                    <button class="btn btn-sm btn-outline-primary select-vehicle"
                            data-vehicle-id="${vehicle.id}">选择</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 车牌号验证
     */
    validateVehiclePlate(plate) {
        if (!plate) {
            return { valid: false, message: '车牌号不能为空' };
        }

        const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
        if (!plateRegex.test(plate)) {
            return { valid: false, message: '车牌号格式不正确' };
        }

        return { valid: true, message: '' };
    }

    /**
     * 用车时间验证
     */
    validateUsageTime() {
        const startTime = new Date($('#useStartTime').val());
        const endTime = new Date($('#useEndTime').val());
        const now = new Date();

        // 开始时间不能早于当前时间2小时
        const minStartTime = new Date(now.getTime() + 2 * 60 * 60 * 1000);
        if (startTime < minStartTime) {
            this.showFieldValidation('useStartTime', {
                valid: false,
                message: '用车时间不能早于当前时间2小时'
            });
            return false;
        }

        // 结束时间必须晚于开始时间
        if (endTime <= startTime) {
            this.showFieldValidation('useEndTime', {
                valid: false,
                message: '用车结束时间必须晚于开始时间'
            });
            return false;
        }

        // 用车时长不能超过7天
        const duration = (endTime - startTime) / (1000 * 60 * 60 * 24);
        if (duration > 7) {
            this.showFieldValidation('useEndTime', {
                valid: false,
                message: '单次用车时长不能超过7天'
            });
            return false;
        }

        // 清除错误提示
        this.clearFieldValidation('useStartTime');
        this.clearFieldValidation('useEndTime');

        // 重新获取可用车辆
        this.refreshAvailableVehicles();

        return true;
    }

    /**
     * 预估费用计算
     */
    calculateEstimatedCost() {
        const mileage = parseInt($('#estimatedMileage').val()) || 0;
        const fuelType = parseInt($('#fuelType').val()) || 1;

        if (mileage > 0) {
            // 根据里程和燃料类型计算预估费用
            const fuelPrice = this.getFuelPrice(fuelType);
            const fuelConsumption = this.getAverageFuelConsumption();

            const fuelCost = (mileage / 100) * fuelConsumption * fuelPrice;
            const tollCost = mileage * 0.5; // 过路费估算
            const driverCost = this.getDriverCost();

            const totalCost = fuelCost + tollCost + driverCost;

            $('#estimatedCost').val(totalCost.toFixed(2));
            this.showCostBreakdown(fuelCost, tollCost, driverCost);
        }
    }

    /**
     * 显示费用明细
     */
    showCostBreakdown(fuelCost, tollCost, driverCost) {
        const breakdownHtml = `
            <div class="cost-breakdown">
                <div class="cost-item">
                    <span class="cost-label">燃油费：</span>
                    <span class="cost-value">¥${fuelCost.toFixed(2)}</span>
                </div>
                <div class="cost-item">
                    <span class="cost-label">过路费：</span>
                    <span class="cost-value">¥${tollCost.toFixed(2)}</span>
                </div>
                <div class="cost-item">
                    <span class="cost-label">司机费：</span>
                    <span class="cost-value">¥${driverCost.toFixed(2)}</span>
                </div>
                <div class="cost-total">
                    <span class="cost-label">总计：</span>
                    <span class="cost-value">¥${(fuelCost + tollCost + driverCost).toFixed(2)}</span>
                </div>
            </div>
        `;

        $('#costBreakdown').html(breakdownHtml);
    }

    /**
     * 提交申请
     */
    async submitApplication() {
        this.showLoading('正在提交申请...');

        try {
            const formData = this.collectFormData();
            const response = await this.apiCall('/api/vehicle/application/create', formData);

            if (response.success) {
                this.showSuccess('申请提交成功！', () => {
                    window.location.href = '/vehicle/application/success?id=' + response.data.id;
                });
            } else {
                this.showError(response.message);
            }
        } catch (error) {
            this.showError('提交失败，请重试');
        } finally {
            this.hideLoading();
        }
    }
}
```

#### B. 移动端适配方案

```css
/* 车辆管理移动端响应式设计 */
@media (max-width: 768px) {
    .vehicle-application-form {
        padding: 10px;
    }

    .step-indicator {
        flex-direction: column;
        gap: 8px;
    }

    .step-item {
        font-size: 12px;
        padding: 8px;
    }

    .vehicle-selector {
        width: 100%;
        margin-bottom: 15px;
    }

    .vehicle-item {
        flex-direction: column;
        padding: 15px;
    }

    .vehicle-info {
        margin-bottom: 10px;
    }

    .vehicle-actions {
        width: 100%;
        justify-content: center;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
        border-radius: 6px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        padding: 12px;
        font-size: 16px;
    }

    .cost-breakdown {
        font-size: 14px;
    }

    .vehicle-selection-modal {
        width: 95%;
        height: 90%;
        margin: 5% auto;
    }

    .modal-body {
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }
}

/* 触摸优化 */
.touch-device .form-control {
    min-height: 44px; /* iOS推荐最小触摸区域 */
}

.touch-device .btn {
    min-height: 44px;
    padding: 12px 20px;
}

.touch-device .vehicle-item {
    min-height: 60px;
    padding: 15px;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .vehicle-application-form {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .form-control {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }

    .vehicle-item {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .vehicle-selection-modal {
        background-color: #1a1a1a;
        color: #ffffff;
    }
}
```

### 运维监控深度细化

#### A. 系统监控指标设计

**1. 业务监控指标**

```java
/**
 * 车辆管理业务监控服务
 */
@Service
@Slf4j
public class VehicleMonitoringService {

    @Resource
    private MeterRegistry meterRegistry;

    // 业务指标计数器
    private final Counter vehicleApplicationCounter;
    private final Counter vehicleApprovalCounter;
    private final Counter vehicleUsageCounter;
    private final Counter maintenanceCounter;

    // 业务指标计时器
    private final Timer approvalProcessTimer;
    private final Timer vehicleScheduleTimer;
    private final Timer maintenanceProcessTimer;

    // 业务指标仪表盘
    private final Gauge pendingApplicationsGauge;
    private final Gauge availableVehiclesGauge;
    private final Gauge vehicleUtilizationGauge;

    public VehicleMonitoringService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化计数器
        this.vehicleApplicationCounter = Counter.builder("vehicle.application.submitted")
            .description("车辆申请提交总数")
            .register(meterRegistry);

        this.vehicleApprovalCounter = Counter.builder("vehicle.application.approved")
            .description("车辆申请审批通过总数")
            .register(meterRegistry);

        this.vehicleUsageCounter = Counter.builder("vehicle.usage.started")
            .description("车辆使用开始总数")
            .register(meterRegistry);

        // 初始化计时器
        this.approvalProcessTimer = Timer.builder("vehicle.approval.process.time")
            .description("车辆审批流程处理时间")
            .register(meterRegistry);

        this.vehicleScheduleTimer = Timer.builder("vehicle.schedule.time")
            .description("车辆调度处理时间")
            .register(meterRegistry);

        // 初始化仪表盘
        this.pendingApplicationsGauge = Gauge.builder("vehicle.application.pending.count")
            .description("待处理车辆申请数量")
            .register(meterRegistry, this, VehicleMonitoringService::getPendingApplicationCount);

        this.availableVehiclesGauge = Gauge.builder("vehicle.available.count")
            .description("可用车辆数量")
            .register(meterRegistry, this, VehicleMonitoringService::getAvailableVehicleCount);
    }

    /**
     * 记录车辆申请提交
     */
    public void recordVehicleApplication(String applicationType, String department) {
        vehicleApplicationCounter.increment(
            Tags.of(
                "application_type", applicationType,
                "department", department
            )
        );
    }

    /**
     * 记录车辆调度
     */
    public void recordVehicleSchedule(long scheduleTimeMs, boolean success, String vehicleType) {
        vehicleScheduleTimer.record(scheduleTimeMs, TimeUnit.MILLISECONDS);

        Counter.builder("vehicle.schedule.result")
            .description("车辆调度结果")
            .tag("success", String.valueOf(success))
            .tag("vehicle_type", vehicleType)
            .register(meterRegistry)
            .increment();
    }

    /**
     * 记录车辆使用
     */
    public void recordVehicleUsage(String vehicleType, long usageDurationHours) {
        vehicleUsageCounter.increment(Tags.of("vehicle_type", vehicleType));

        Gauge.builder("vehicle.usage.duration.hours")
            .description("车辆使用时长")
            .tag("vehicle_type", vehicleType)
            .register(meterRegistry, this, (self) -> usageDurationHours);
    }

    /**
     * 自定义业务告警
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkBusinessAlerts() {
        // 检查待处理申请积压
        long pendingCount = getPendingApplicationCount();
        if (pendingCount > 20) {
            sendAlert("VEHICLE_APPLICATIONS_HIGH",
                "待处理车辆申请数量过多：" + pendingCount,
                AlertLevel.WARNING);
        }

        // 检查车辆利用率
        double utilizationRate = calculateVehicleUtilizationRate();
        if (utilizationRate < 0.3) {
            sendAlert("VEHICLE_UTILIZATION_LOW",
                "车辆利用率过低：" + String.format("%.2f%%", utilizationRate * 100),
                AlertLevel.INFO);
        } else if (utilizationRate > 0.9) {
            sendAlert("VEHICLE_UTILIZATION_HIGH",
                "车辆利用率过高：" + String.format("%.2f%%", utilizationRate * 100),
                AlertLevel.WARNING);
        }

        // 检查维保超期车辆
        List<VehicleCompanyDO> overdueVehicles = findOverdueMaintenanceVehicles();
        if (!overdueVehicles.isEmpty()) {
            sendAlert("MAINTENANCE_OVERDUE",
                "发现" + overdueVehicles.size() + "辆车辆维保超期",
                AlertLevel.CRITICAL);
        }
    }

    private double getPendingApplicationCount() {
        return vehicleApplicationMapper.countByStatus(ApplicationStatusEnum.PENDING_APPROVAL.getStatus());
    }

    private double getAvailableVehicleCount() {
        return vehicleCompanyMapper.countByStatus(CompanyVehicleStatusEnum.NORMAL.getStatus());
    }
}
```

#### B. 日志记录规范

**1. 结构化日志设计**

```java
/**
 * 车辆管理日志服务
 */
@Service
@Slf4j
public class VehicleLoggingService {

    private static final String LOG_PREFIX = "[VEHICLE]";

    /**
     * 记录车辆业务操作日志
     */
    public void logVehicleOperation(String operation, Long targetId, String targetType,
                                  Long userId, Object requestData, Object responseData, long duration) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("operation", operation);
        logData.put("targetId", targetId);
        logData.put("targetType", targetType);
        logData.put("userId", userId);
        logData.put("duration", duration);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("requestData", requestData);
        logData.put("responseData", responseData);

        log.info("{} 车辆业务操作 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录车辆安全事件日志
     */
    public void logVehicleSecurityEvent(String eventType, String description, String vehiclePlate,
                                      String ipAddress, String userAgent, Long userId) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("eventType", eventType);
        logData.put("description", description);
        logData.put("vehiclePlate", vehiclePlate);
        logData.put("ipAddress", ipAddress);
        logData.put("userAgent", userAgent);
        logData.put("userId", userId);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("severity", "HIGH");

        log.warn("{} 车辆安全事件 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录车辆调度日志
     */
    public void logVehicleSchedule(Long applicationId, Long vehicleId, Long driverId,
                                 String scheduleResult, String scheduleReason) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("applicationId", applicationId);
        logData.put("vehicleId", vehicleId);
        logData.put("driverId", driverId);
        logData.put("scheduleResult", scheduleResult);
        logData.put("scheduleReason", scheduleReason);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("type", "VEHICLE_SCHEDULE");

        log.info("{} 车辆调度 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录车辆维保日志
     */
    public void logVehicleMaintenance(Long vehicleId, String maintenanceType, String maintenanceResult,
                                    BigDecimal cost, String garage) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("vehicleId", vehicleId);
        logData.put("maintenanceType", maintenanceType);
        logData.put("maintenanceResult", maintenanceResult);
        logData.put("cost", cost);
        logData.put("garage", garage);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("type", "VEHICLE_MAINTENANCE");

        log.info("{} 车辆维保 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }
}
```

**2. 日志配置**

```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- 车辆业务日志文件 -->
    <appender name="VEHICLE_BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/vehicle-business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/vehicle-business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[VEHICLE] 车辆业务操作")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 车辆安全日志文件 -->
    <appender name="VEHICLE_SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/vehicle-security.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/vehicle-security.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <message/>
                <mdc/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[VEHICLE] 车辆安全事件")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 车辆调度日志文件 -->
    <appender name="VEHICLE_SCHEDULE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/vehicle-schedule.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/vehicle-schedule.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <message/>
                <mdc/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[VEHICLE] 车辆调度")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>
</configuration>
```

#### C. 故障排查和应急处理

**1. 故障诊断工具**

```java
/**
 * 车辆管理故障诊断服务
 */
@RestController
@RequestMapping("/api/admin/vehicle/diagnosis")
@PreAuthorize("hasRole('ADMIN')")
public class VehicleDiagnosisController {

    @Resource
    private VehicleApplicationMapper applicationMapper;

    @Resource
    private VehicleCompanyMapper vehicleCompanyMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 车辆系统健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> vehicleHealthCheck() {
        Map<String, Object> health = new HashMap<>();

        // 数据库连接检查
        health.put("database", checkVehicleDatabaseHealth());

        // 缓存检查
        health.put("cache", checkVehicleCacheHealth());

        // 业务数据检查
        health.put("business", checkVehicleBusinessHealth());

        // 外部服务检查
        health.put("external", checkVehicleExternalServicesHealth());

        return Result.success(health);
    }

    /**
     * 车辆调度诊断
     */
    @GetMapping("/schedule")
    public Result<Map<String, Object>> vehicleScheduleDiagnosis() {
        Map<String, Object> diagnosis = new HashMap<>();

        // 车辆可用性检查
        diagnosis.put("vehicleAvailability", analyzeVehicleAvailability());

        // 司机可用性检查
        diagnosis.put("driverAvailability", analyzeDriverAvailability());

        // 调度算法性能
        diagnosis.put("schedulePerformance", analyzeSchedulePerformance());

        // 冲突检测
        diagnosis.put("conflicts", detectScheduleConflicts());

        return Result.success(diagnosis);
    }

    /**
     * 车辆数据一致性检查
     */
    @GetMapping("/consistency")
    public Result<Map<String, Object>> vehicleConsistencyCheck() {
        Map<String, Object> consistency = new HashMap<>();

        // 检查车辆申请与流程数据一致性
        List<String> inconsistentApplications = findInconsistentVehicleApplications();
        consistency.put("inconsistentApplications", inconsistentApplications);

        // 检查车辆状态一致性
        List<String> inconsistentVehicles = findInconsistentVehicleStatus();
        consistency.put("inconsistentVehicles", inconsistentVehicles);

        // 检查缓存数据一致性
        List<String> inconsistentCache = findInconsistentVehicleCache();
        consistency.put("inconsistentCache", inconsistentCache);

        return Result.success(consistency);
    }

    /**
     * 修复车辆数据不一致
     */
    @PostMapping("/repair")
    public Result<String> repairVehicleDataInconsistency(@RequestBody VehicleRepairRequest request) {
        try {
            switch (request.getType()) {
                case "application_status":
                    repairVehicleApplicationStatus(request.getIds());
                    break;
                case "vehicle_status":
                    repairVehicleStatus(request.getIds());
                    break;
                case "cache_data":
                    repairVehicleCacheData(request.getIds());
                    break;
                case "schedule_data":
                    repairVehicleScheduleData(request.getIds());
                    break;
                default:
                    return Result.error("未知的修复类型");
            }

            return Result.success("修复完成");
        } catch (Exception e) {
            log.error("车辆数据修复失败", e);
            return Result.error("修复失败：" + e.getMessage());
        }
    }

    private Map<String, Object> checkVehicleDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            int vehicleCount = vehicleCompanyMapper.selectCount(null);
            int applicationCount = applicationMapper.selectCount(null);
            long responseTime = System.currentTimeMillis() - startTime;

            dbHealth.put("status", "UP");
            dbHealth.put("responseTime", responseTime);
            dbHealth.put("vehicleCount", vehicleCount);
            dbHealth.put("applicationCount", applicationCount);

        } catch (Exception e) {
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }

        return dbHealth;
    }

    private Map<String, Object> analyzeVehicleAvailability() {
        Map<String, Object> availability = new HashMap<>();

        // 统计各类型车辆可用性
        List<VehicleAvailabilityStats> stats = calculateVehicleAvailabilityStats();
        availability.put("stats", stats);

        // 识别瓶颈车型
        List<String> bottleneckTypes = identifyBottleneckVehicleTypes();
        availability.put("bottlenecks", bottleneckTypes);

        // 预测未来可用性
        Map<String, Object> forecast = forecastVehicleAvailability();
        availability.put("forecast", forecast);

        return availability;
    }
}
```

**2. 应急处理预案**

```java
/**
 * 车辆管理应急处理服务
 */
@Service
@Slf4j
public class VehicleEmergencyService {

    @Resource
    private VehicleNotificationService notificationService;

    @Resource
    private VehicleScheduleService scheduleService;

    /**
     * 车辆系统降级处理
     */
    public void activateVehicleSystemDegradation(DegradationLevel level) {
        switch (level) {
            case LEVEL_1: // 轻度降级
                // 关闭非核心功能
                disableNonCriticalVehicleFeatures();
                break;

            case LEVEL_2: // 中度降级
                // 启用简化调度模式
                enableSimplifiedScheduleMode();
                break;

            case LEVEL_3: // 重度降级
                // 启用手动调度模式
                enableManualScheduleMode();
                break;

            case EMERGENCY: // 紧急模式
                // 系统维护模式
                enableVehicleMaintenanceMode();
                break;
        }

        // 通知相关人员
        notifyVehicleEmergencyResponse(level);
    }

    /**
     * 车辆调度故障自动恢复
     */
    @EventListener
    public void handleVehicleScheduleFailure(VehicleScheduleFailureEvent event) {
        try {
            // 记录故障信息
            recordVehicleFailureInfo(event);

            // 尝试自动恢复
            boolean recovered = attemptVehicleAutoRecovery(event.getFailureType());

            if (recovered) {
                log.info("车辆调度故障自动恢复成功：{}", event.getFailureType());
                notifyVehicleRecoverySuccess(event);
            } else {
                log.error("车辆调度故障自动恢复失败：{}", event.getFailureType());
                escalateToVehicleManualIntervention(event);
            }

        } catch (Exception e) {
            log.error("车辆故障处理异常", e);
            escalateToVehicleManualIntervention(event);
        }
    }

    /**
     * 车辆紧急调度
     */
    public VehicleEmergencyScheduleResult emergencyVehicleSchedule(VehicleEmergencyRequest request) {
        try {
            // 1. 获取所有可用车辆（包括预约中的）
            List<VehicleCompanyDO> allVehicles = getAllAvailableVehicles();

            // 2. 紧急调度算法（优先级更高）
            VehicleScheduleResult result = executeEmergencySchedule(allVehicles, request);

            // 3. 如果需要，取消低优先级预约
            if (!result.isSuccess()) {
                result = scheduleWithCancellation(request);
            }

            // 4. 通知相关人员
            notifyEmergencyScheduleResult(result, request);

            return VehicleEmergencyScheduleResult.fromScheduleResult(result);

        } catch (Exception e) {
            log.error("紧急车辆调度失败", e);
            return VehicleEmergencyScheduleResult.error("紧急调度失败");
        }
    }

    private boolean attemptVehicleAutoRecovery(String failureType) {
        switch (failureType) {
            case "SCHEDULE_ALGORITHM_FAILED":
                return recoverScheduleAlgorithm();

            case "VEHICLE_DATA_INCONSISTENT":
                return recoverVehicleDataConsistency();

            case "EXTERNAL_SERVICE_TIMEOUT":
                return recoverExternalVehicleService();

            case "CACHE_SERVICE_FAILED":
                return recoverVehicleCacheService();

            default:
                return false;
        }
    }

    private void escalateToVehicleManualIntervention(VehicleScheduleFailureEvent event) {
        // 发送紧急通知
        notificationService.sendVehicleEmergencyAlert(
            "车辆系统故障需要人工干预",
            "故障类型：" + event.getFailureType() + "\n" +
            "故障时间：" + event.getTimestamp() + "\n" +
            "故障描述：" + event.getDescription()
        );

        // 记录到故障处理系统
        recordVehicleManualInterventionRequired(event);
    }
}
```

## 前端页面设计

### 页面结构设计

#### 1. 车辆申请页面

**页面路径：** `/vehicle/application/create`

**页面组件结构：**

```vue
<template>
  <div class="vehicle-application-page">
    <!-- 页面头部 -->
    <page-header title="车辆申请" :breadcrumb="breadcrumbItems" />

    <!-- 步骤指示器 -->
    <step-indicator :current="currentStep" :steps="steps" />

    <!-- 申请表单 -->
    <div class="application-form-container">
      <el-form ref="applicationForm" :model="formData" :rules="formRules" label-width="120px">

        <!-- 步骤1：申请类型选择 -->
        <div v-show="currentStep === 1" class="form-step">
          <div class="step-title">选择申请类型</div>
          <el-radio-group v-model="formData.applicationType" @change="onApplicationTypeChange">
            <el-radio-button :label="1">公车使用申请</el-radio-button>
            <el-radio-button :label="2">维保申请</el-radio-button>
            <el-radio-button :label="3">加油申请</el-radio-button>
            <el-radio-button :label="4">年检申请</el-radio-button>
            <el-radio-button :label="5">保险申请</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 步骤2：基本信息 -->
        <div v-show="currentStep === 2" class="form-step">
          <div class="step-title">填写基本信息</div>

          <!-- 公车使用申请字段 -->
          <template v-if="formData.applicationType === 1">
            <el-form-item label="选择车辆" prop="vehicleId" required>
              <vehicle-selector
                v-model="formData.vehicleId"
                :start-time="formData.useStartTime"
                :end-time="formData.useEndTime"
                :passenger-count="formData.passengerCount"
                @change="onVehicleChange" />
            </el-form-item>

            <el-form-item label="用车目的" prop="usePurpose" required>
              <el-input v-model="formData.usePurpose" placeholder="请输入用车目的" maxlength="200" show-word-limit />
            </el-form-item>

            <el-form-item label="用车时间" required>
              <el-col :span="11">
                <el-form-item prop="useStartTime">
                  <el-date-picker
                    v-model="formData.useStartTime"
                    type="datetime"
                    placeholder="开始时间"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="onTimeChange" />
                </el-form-item>
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="form-text">至</span>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="useEndTime">
                  <el-date-picker
                    v-model="formData.useEndTime"
                    type="datetime"
                    placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="onTimeChange" />
                </el-form-item>
              </el-col>
            </el-form-item>
          </template>

          <!-- 维保申请字段 -->
          <template v-if="formData.applicationType === 2">
            <el-form-item label="选择车辆" prop="vehicleId" required>
              <vehicle-selector v-model="formData.vehicleId" :filter-type="'maintenance'" />
            </el-form-item>

            <el-form-item label="维保类型" prop="maintenanceType" required>
              <el-select v-model="formData.maintenanceType" placeholder="请选择维保类型">
                <el-option label="定期保养" :value="1" />
                <el-option label="故障维修" :value="2" />
                <el-option label="事故维修" :value="3" />
                <el-option label="改装升级" :value="4" />
              </el-select>
            </el-form-item>
          </template>
        </div>

        <!-- 步骤3：详细信息 -->
        <div v-show="currentStep === 3" class="form-step">
          <div class="step-title">完善详细信息</div>
          <!-- 根据申请类型显示不同的详细信息表单 -->
        </div>

        <!-- 步骤4：确认提交 -->
        <div v-show="currentStep === 4" class="form-step">
          <div class="step-title">确认申请信息</div>
          <application-summary :form-data="formData" />
        </div>

      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button v-if="currentStep > 1" @click="prevStep">上一步</el-button>
      <el-button v-if="currentStep < 4" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="currentStep === 4" type="primary" :loading="submitting" @click="submitApplication">
        提交申请
      </el-button>
    </div>
  </div>
</template>
```

## 测试用例设计

### 单元测试用例

#### 1. 车辆调度算法测试

```java
@SpringBootTest
class VehicleScheduleServiceTest {

    @Resource
    private VehicleScheduleService vehicleScheduleService;

    @Test
    @DisplayName("测试车辆调度算法 - 正常场景")
    void testVehicleSchedule_Normal() {
        // Given
        VehicleApplicationDO application = createTestApplication();

        // When
        VehicleScheduleResult result = vehicleScheduleService.scheduleVehicle(application);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getVehicle()).isNotNull();
        assertThat(result.getDriver()).isNotNull();
    }

    @Test
    @DisplayName("测试车辆调度算法 - 无可用车辆")
    void testVehicleSchedule_NoAvailableVehicle() {
        // Given
        VehicleApplicationDO application = createTestApplicationWithConflictTime();

        // When
        VehicleScheduleResult result = vehicleScheduleService.scheduleVehicle(application);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getErrorMessage()).contains("无可用车辆");
    }
}
```
