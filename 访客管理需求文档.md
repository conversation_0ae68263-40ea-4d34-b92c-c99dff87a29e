3.2.1.来访人员管理模块
3.2.1.1 普通访客到访流程
 
1）访客在服务号h5端进行预约到访申请，在【人员到访申请】入口进入；
2）【来访入厂申请单】，由访客主动发起，含如下字段：来访人单位、入厂人员姓名、联系方式、入厂事由、厂内联系人、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区, 访客类型（参观/商务）、照片；是否驾车，车牌号，同行人数量，同行人姓名、联系方式、照片；是否需要住宿；是否前往饭堂就餐；
3）申请单审批流程按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；
4）审批后访客可在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；
5）警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；
6）警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；
7）警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；
8）各项通知：访客发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知访客；警卫在H5的【现场查询】可以收到新增红点提醒；
9）申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；


3.2.1.2政府访客到访流程

1）员工在园区管理系统【人员到访申请】进行预约到访申请，对接企业微信移动端和pc端；
2）【来访入厂申请单】，由员工发起，含如下字段：带出员工信息（厂内联系人姓名、联系方式、部门）、来访人单位、入厂人员姓名、联系方式、入厂事由、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区, 访客类型（参观/商务）、照片；是否驾车，车牌号，同行人数量，同行人姓名、联系方式、照片；是否需要住宿；是否前往饭堂就餐；
3）申请单审批流程按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；
4）审批后访客会收到手机短信，通过短信链接在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；
5）警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；
6）警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；
7）警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；
8）各项通知：员工发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知施工承包商；警卫在H5的【现场查询】可以收到新增红点提醒；
9）申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；

3.2.1.3施工承包商访客到访流程

1）承包商人员在服务号h5端进行预约到访申请，在【人员到访申请】入口进入；
2）【来访入厂申请单】，由承包商人员主动发起，含如下字段：来访人单位、入厂人员姓名、联系方式、身份证号码、入厂事由、厂内联系人、预计到访时间段（根据预约到访时间段控制二维码有效性），到访方式（随车入园、各自入园），到访厂区, 访客类型（施工承包商）、照片；是否驾车，车辆类型（普通客车、施工车）、车牌号，若是施工车，需填写行驶证编号和上传照片；同行人数量，同行人姓名、联系方式、身份证号码、照片；是否需要住宿；是否前往饭堂就餐；
3）保存后，需完成入场培训并签字后，提交申请单到管理员审批；入场培训可以在【培训管理】配置；


培训附件支持图文pdf或者视频，支持一种格式的多个文件上传，支持培训最小时长设置；
4）承包商施工申请单提交后，对接承包商系统数据，通过身份证号匹配人员所属项目；若员工在企业微信主动发起申请，可主动选择施工承包商和项目信息；
5）申请单审批流程按角色配置，流程节点依次为：厂内联系人、联系人部门主管、综管部负责人审核后，抄送警卫（可配置）；管理员可在手机端和pc端进行流程审批；若警卫不在企业微信，可配置账号登录到园区管理系统；
6）审批后施工承包商访客可在H5【我的】-【人员到访申请】查看到访预约申请表，签署入园安全培训须知后，生成带有照片的通行二维码，二维码根据预约到访时间段控制二维码有效性；
7）警卫在H5【人员签到登记】扫二维码确认到访（判断二维码有效性），记录入园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；
8）警卫在H5【人员签到登记】扫二维码确认离场（判断二维码有效性），记录出园时间；若有访客车在H5【车辆签到登记】，可配置管理到车还是人；若管理到车，仅需车主扫码确认；若管理到人，车上访客均需扫码确认；拍照到访车辆，车辆识别放行；对于车辆尾箱抽检异常情况，在H5【异常登记】进行记录；
9）警卫在H5【到访人员管理】可以对来访人员进行搜索查询；在H5【到访车辆管理】可以对来访车辆进行搜索查询；在H5【园区车辆管理】可以对园区车辆进行搜索查询；
10）各项通知：施工承包商发起申请单，通过企业微信通知厂内联系人和审批管理员；管理员审批完申请单，短信通知施工承包商；警卫在H5的【现场查询】可以收到新增红点提醒；
11）申请单状态分别为：提交后为【待确认】，审批通过后为【已审批】，驳回后为【已驳回】，签到入园后为【已入园】，签到出园后为【已出园】；

3.2.1.4施工承包商人员变更到访流程

1）整体流程与施工承包商到访流程一样，访客类型为承包商人员变更；
3.2.1.5到访人员列表-pc端
1）分常规人员到访和施工承包商人员到访，详见原型；
2）可进行【详情】、【入园签到】、【出园签到】、【进出记录】操作；
【详情】：可查看申请单详情，管理员在详情页内可以进行审批操作；
【入园签到】：可手动对到访人员进行入园签到，点击后弹窗确认，状态变成【已入园】；
【出园签到】：可手动对到访人员进行出园签到，点击后弹窗确认，状态变成【已出园】；若是承包商，注销施工许可证；
【进出记录】：可查看访客或施工承包商人员的进出记录；