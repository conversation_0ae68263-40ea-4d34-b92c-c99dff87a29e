# 接待管理模块详细设计文档

## 文档信息

| 项目名称 | 接待管理模块 |
|---------|-------------|
| 模块标识 | yudao-module-reception |
| 文档版本 | 2.0 |
| 编写日期 | 2025-08-11 |
| 编写人员 | 边浩澜 |

## 目录

1. [模块概述](#模块概述)
2. [数据库设计](#数据库设计)
3. [API接口设计](#API接口设计)
4. [业务流程设计](#业务流程设计)
5. [技术实现深度细化](#技术实现深度细化)
6. [集成方案深度细化](#集成方案深度细化)
7. [用户体验深度细化](#用户体验深度细化)
8. [运维监控深度细化](#运维监控深度细化)
9. [前端页面设计](#前端页面设计)
10. [测试用例设计](#测试用例设计)

## 模块概述

### 业务范围

接待管理模块负责园区访客的接待服务全生命周期管理，包括接待申请、住宿安排、就餐管理、费用结算等完整流程。支持与访客管理模块的深度集成，为访客提供一站式接待服务。

### 核心功能

- **接待申请管理**：住宿接待申请、就餐申请、审批流程
- **住宿服务管理**：房间分配、入住登记、退宿管理
- **就餐服务管理**：饭卡申领、就餐记录、人脸识别对接
- **费用管理**：住宿费用、餐费计算、账单生成
- **资源管理**：客房管理、餐厅管理、设施维护
- **统计分析**：接待统计、费用分析、满意度调查

### 技术架构

**后端技术栈：**

- Spring Boot 3.x + Spring Security 6.x
- MyBatis Plus + MySQL 8.0
- Flowable 7.x 工作流引擎
- Redis 7.x 缓存
- RabbitMQ 消息队列

**前端技术栈：**

- Vue 3.x + Element Plus（PC管理端）
- Vant 4.x（H5移动端）
- TypeScript + Vite

**集成方案：**

- 企业微信API集成
- 短信服务集成
- 人脸识别系统集成
- 饭卡系统集成

### 模块依赖关系

```mermaid
graph TB
    RM[接待管理模块] --> VM[访客管理模块]
    RM --> AM[住宿管理模块]
    RM --> BPM[工作流引擎]
    RM --> NS[通知服务]
    RM --> FS[文件服务]
    RM --> PS[支付服务]
    
    VM -.->|访客信息| RM
    AM -.->|房间资源| RM
    BPM -.->|审批流程| RM
    NS -.->|消息通知| RM
    FS -.->|文件存储| RM
    PS -.->|费用结算| RM
```

## 数据库设计

### 核心业务表设计

#### 1. 接待申请表 (reception_application)

```sql
CREATE TABLE reception_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_no VARCHAR(50) NOT NULL COMMENT '申请单号',
    visitor_application_id BIGINT NOT NULL COMMENT '关联访客申请ID',
    visitor_application_no VARCHAR(50) NOT NULL COMMENT '关联访客申请单号',
    applicant_id BIGINT COMMENT '申请人ID',
    applicant_name VARCHAR(50) NOT NULL COMMENT '申请人姓名',
    applicant_phone VARCHAR(20) COMMENT '申请人电话',
    applicant_dept_id BIGINT COMMENT '申请人部门ID',
    applicant_dept_name VARCHAR(100) COMMENT '申请人部门名称',
    
    -- 访客信息
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    visitor_phone VARCHAR(20) NOT NULL COMMENT '访客电话',
    visitor_company VARCHAR(100) COMMENT '访客单位',
    visitor_count INT DEFAULT 1 COMMENT '访客人数',
    visitor_list JSON COMMENT '访客名单JSON',
    
    -- 接待服务信息
    reception_type TINYINT NOT NULL COMMENT '接待类型：1-仅住宿 2-仅就餐 3-住宿+就餐',
    reception_level TINYINT DEFAULT 1 COMMENT '接待级别：1-普通 2-重要 3-VIP',
    reception_reason VARCHAR(200) COMMENT '接待原因',
    
    -- 住宿信息
    need_accommodation TINYINT DEFAULT 0 COMMENT '是否需要住宿：0-否 1-是',
    check_in_date DATE COMMENT '入住日期',
    check_out_date DATE COMMENT '退宿日期',
    accommodation_days INT COMMENT '住宿天数',
    room_type TINYINT COMMENT '房间类型：1-标准间 2-单人间 3-套房',
    room_count INT COMMENT '房间数量',
    special_requirements VARCHAR(500) COMMENT '特殊要求',
    
    -- 就餐信息
    need_dining TINYINT DEFAULT 0 COMMENT '是否需要就餐：0-否 1-是',
    dining_start_date DATE COMMENT '就餐开始日期',
    dining_end_date DATE COMMENT '就餐结束日期',
    dining_days INT COMMENT '就餐天数',
    meal_standard TINYINT COMMENT '用餐标准：1-普通 2-商务 3-高级',
    breakfast_count INT DEFAULT 0 COMMENT '早餐人次',
    lunch_count INT DEFAULT 0 COMMENT '午餐人次',
    dinner_count INT DEFAULT 0 COMMENT '晚餐人次',
    
    -- 费用信息
    estimated_accommodation_cost DECIMAL(10,2) DEFAULT 0 COMMENT '预估住宿费用',
    estimated_dining_cost DECIMAL(10,2) DEFAULT 0 COMMENT '预估餐费',
    estimated_total_cost DECIMAL(10,2) DEFAULT 0 COMMENT '预估总费用',
    actual_accommodation_cost DECIMAL(10,2) DEFAULT 0 COMMENT '实际住宿费用',
    actual_dining_cost DECIMAL(10,2) DEFAULT 0 COMMENT '实际餐费',
    actual_total_cost DECIMAL(10,2) DEFAULT 0 COMMENT '实际总费用',
    cost_bearer TINYINT COMMENT '费用承担方：1-公司承担 2-访客自付 3-部门承担',
    
    -- 审批流程字段
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待审批 2-已审批 3-已驳回 4-服务中 5-已完成 6-已取消',
    approval_result TINYINT COMMENT '审批结果：1-通过 2-驳回',
    approval_reason VARCHAR(200) COMMENT '审批意见',
    approval_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(50) COMMENT '审批人姓名',
    
    -- 执行结果字段
    actual_check_in_time DATETIME COMMENT '实际入住时间',
    actual_check_out_time DATETIME COMMENT '实际退宿时间',
    service_rating TINYINT COMMENT '服务评分（1-5分）',
    service_feedback TEXT COMMENT '服务反馈',
    
    -- 流程字段
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_application_no (application_no),
    INDEX idx_visitor_application_id (visitor_application_id),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_status (status),
    INDEX idx_reception_type (reception_type),
    INDEX idx_check_in_date (check_in_date),
    INDEX idx_create_time (create_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待申请表';
```

#### 2. 住宿安排表 (reception_accommodation)

```sql
CREATE TABLE reception_accommodation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    accommodation_no VARCHAR(50) NOT NULL COMMENT '住宿单号',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    application_no VARCHAR(50) NOT NULL COMMENT '接待申请单号',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    visitor_phone VARCHAR(20) NOT NULL COMMENT '访客电话',
    
    -- 房间信息
    room_id BIGINT NOT NULL COMMENT '房间ID',
    room_no VARCHAR(20) NOT NULL COMMENT '房间号',
    room_type TINYINT NOT NULL COMMENT '房间类型：1-标准间 2-单人间 3-套房',
    building_name VARCHAR(50) COMMENT '楼栋名称',
    floor_no INT COMMENT '楼层',
    
    -- 入住信息
    planned_check_in_date DATE NOT NULL COMMENT '计划入住日期',
    planned_check_out_date DATE NOT NULL COMMENT '计划退宿日期',
    planned_days INT NOT NULL COMMENT '计划住宿天数',
    actual_check_in_time DATETIME COMMENT '实际入住时间',
    actual_check_out_time DATETIME COMMENT '实际退宿时间',
    actual_days INT COMMENT '实际住宿天数',
    
    -- 费用信息
    daily_rate DECIMAL(8,2) NOT NULL COMMENT '日房费',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总费用',
    deposit_amount DECIMAL(8,2) DEFAULT 0 COMMENT '押金金额',
    deposit_paid TINYINT DEFAULT 0 COMMENT '押金是否已缴：0-否 1-是',
    deposit_refunded TINYINT DEFAULT 0 COMMENT '押金是否已退：0-否 1-是',
    
    -- 状态信息
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-已分配 2-已入住 3-已退宿 4-已取消',
    check_in_operator_id BIGINT COMMENT '入住操作员ID',
    check_in_operator_name VARCHAR(50) COMMENT '入住操作员姓名',
    check_out_operator_id BIGINT COMMENT '退宿操作员ID',
    check_out_operator_name VARCHAR(50) COMMENT '退宿操作员姓名',
    
    -- 服务信息
    key_card_no VARCHAR(20) COMMENT '房卡号',
    wifi_password VARCHAR(50) COMMENT 'WiFi密码',
    service_items JSON COMMENT '服务项目JSON',
    special_notes VARCHAR(500) COMMENT '特殊备注',
    
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_accommodation_no (accommodation_no),
    INDEX idx_application_id (application_id),
    INDEX idx_room_id (room_id),
    INDEX idx_visitor_name (visitor_name),
    INDEX idx_check_in_date (planned_check_in_date),
    INDEX idx_status (status),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='住宿安排表';
```

#### 3. 就餐安排表 (reception_dining)

```sql
CREATE TABLE reception_dining (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    dining_no VARCHAR(50) NOT NULL COMMENT '就餐单号',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    application_no VARCHAR(50) NOT NULL COMMENT '接待申请单号',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    visitor_phone VARCHAR(20) NOT NULL COMMENT '访客电话',
    
    -- 就餐信息
    dining_start_date DATE NOT NULL COMMENT '就餐开始日期',
    dining_end_date DATE NOT NULL COMMENT '就餐结束日期',
    dining_days INT NOT NULL COMMENT '就餐天数',
    meal_standard TINYINT NOT NULL COMMENT '用餐标准：1-普通 2-商务 3-高级',
    
    -- 餐次统计
    total_breakfast_count INT DEFAULT 0 COMMENT '早餐总人次',
    total_lunch_count INT DEFAULT 0 COMMENT '午餐总人次',
    total_dinner_count INT DEFAULT 0 COMMENT '晚餐总人次',
    consumed_breakfast_count INT DEFAULT 0 COMMENT '已消费早餐人次',
    consumed_lunch_count INT DEFAULT 0 COMMENT '已消费午餐人次',
    consumed_dinner_count INT DEFAULT 0 COMMENT '已消费晚餐人次',
    
    -- 费用信息
    breakfast_price DECIMAL(6,2) DEFAULT 0 COMMENT '早餐单价',
    lunch_price DECIMAL(6,2) DEFAULT 0 COMMENT '午餐单价',
    dinner_price DECIMAL(6,2) DEFAULT 0 COMMENT '晚餐单价',
    total_amount DECIMAL(10,2) DEFAULT 0 COMMENT '总费用',
    consumed_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已消费金额',
    
    -- 饭卡信息
    meal_card_required TINYINT DEFAULT 0 COMMENT '是否需要饭卡：0-否 1-是',
    meal_card_no VARCHAR(20) COMMENT '饭卡号',
    meal_card_issued TINYINT DEFAULT 0 COMMENT '饭卡是否已申领：0-否 1-是',
    meal_card_returned TINYINT DEFAULT 0 COMMENT '饭卡是否已归还：0-否 1-是',
    meal_card_issue_time DATETIME COMMENT '饭卡申领时间',
    meal_card_return_time DATETIME COMMENT '饭卡归还时间',
    
    -- 人脸识别信息
    face_recognition_enabled TINYINT DEFAULT 0 COMMENT '是否启用人脸识别：0-否 1-是',
    face_photo_url VARCHAR(500) COMMENT '人脸照片URL',
    face_feature_data TEXT COMMENT '人脸特征数据',
    face_registration_time DATETIME COMMENT '人脸注册时间',
    
    -- 状态信息
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-已安排 2-服务中 3-已完成 4-已取消',
    
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_dining_no (dining_no),
    INDEX idx_application_id (application_id),
    INDEX idx_visitor_name (visitor_name),
    INDEX idx_dining_start_date (dining_start_date),
    INDEX idx_meal_card_no (meal_card_no),
    INDEX idx_status (status),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='就餐安排表';
```

#### 4. 就餐记录表 (reception_dining_record)

```sql
CREATE TABLE reception_dining_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_no VARCHAR(50) NOT NULL COMMENT '记录编号',
    dining_id BIGINT NOT NULL COMMENT '就餐安排ID',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',

    -- 就餐信息
    dining_date DATE NOT NULL COMMENT '就餐日期',
    meal_type TINYINT NOT NULL COMMENT '餐次类型：1-早餐 2-午餐 3-晚餐',
    meal_time TIME NOT NULL COMMENT '就餐时间',
    dining_location VARCHAR(50) COMMENT '就餐地点',

    -- 消费信息
    meal_price DECIMAL(6,2) NOT NULL COMMENT '餐费单价',
    person_count INT DEFAULT 1 COMMENT '就餐人数',
    total_amount DECIMAL(8,2) NOT NULL COMMENT '消费金额',

    -- 支付信息
    payment_method TINYINT COMMENT '支付方式：1-饭卡 2-人脸识别 3-现金 4-公司承担',
    meal_card_no VARCHAR(20) COMMENT '饭卡号',
    transaction_no VARCHAR(50) COMMENT '交易流水号',

    -- 识别信息
    recognition_method TINYINT COMMENT '识别方式：1-饭卡刷卡 2-人脸识别 3-手动录入',
    recognition_confidence DECIMAL(5,2) COMMENT '识别置信度',

    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_record_no (record_no),
    INDEX idx_dining_id (dining_id),
    INDEX idx_application_id (application_id),
    INDEX idx_visitor_name (visitor_name),
    INDEX idx_dining_date (dining_date),
    INDEX idx_meal_type (meal_type),
    INDEX idx_meal_card_no (meal_card_no),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='就餐记录表';
```

#### 5. 接待流程操作记录表 (reception_process_operation)

```sql
CREATE TABLE reception_process_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    task_name VARCHAR(100) COMMENT '任务名称',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-提交申请 2-部门审批 3-综管部审批 4-资源分配 5-服务开始 6-服务结束 7-费用结算 8-流程取消 9-流程驳回',
    operation_result TINYINT COMMENT '操作结果：1-通过 2-驳回 3-取消 4-退回修改',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    operator_type TINYINT COMMENT '操作人类型：1-申请人 2-审批人 3-管理员 4-系统自动',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    operation_duration INT COMMENT '操作耗时（分钟）',
    operation_content TEXT COMMENT '操作内容描述',
    operation_reason VARCHAR(500) COMMENT '操作原因/备注',
    before_status TINYINT COMMENT '操作前状态',
    after_status TINYINT COMMENT '操作后状态',
    form_data JSON COMMENT '表单数据快照',
    attachment_urls JSON COMMENT '附件URL数组',
    ip_address VARCHAR(50) COMMENT '操作IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理信息',
    device_info VARCHAR(200) COMMENT '设备信息',
    location_info VARCHAR(200) COMMENT '位置信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator_id (operator_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待流程操作记录表';
```

#### 6. 接待状态变更历史表 (reception_status_history)

```sql
CREATE TABLE reception_status_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    change_sequence INT NOT NULL COMMENT '变更序号',
    before_status TINYINT COMMENT '变更前状态',
    after_status TINYINT NOT NULL COMMENT '变更后状态',
    status_name VARCHAR(50) COMMENT '状态名称',
    change_reason VARCHAR(200) COMMENT '变更原因',
    change_type TINYINT NOT NULL COMMENT '变更类型：1-正常流转 2-人工干预 3-系统自动 4-异常处理 5-流程回退',
    change_trigger TINYINT COMMENT '变更触发方式：1-用户操作 2-定时任务 3-系统事件 4-外部接口',
    change_person_id BIGINT COMMENT '变更操作人ID',
    change_person_name VARCHAR(50) COMMENT '变更操作人姓名',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    duration_minutes INT COMMENT '在前一状态停留时长（分钟）',
    related_task_id VARCHAR(64) COMMENT '关联的任务ID',
    related_operation_id BIGINT COMMENT '关联的操作记录ID',
    business_data JSON COMMENT '业务数据快照',
    system_variables JSON COMMENT '系统变量快照',
    notification_sent TINYINT DEFAULT 0 COMMENT '是否已发送通知：0-否 1-是',
    notification_time DATETIME COMMENT '通知发送时间',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_change_sequence (application_id, change_sequence),
    INDEX idx_before_status (before_status),
    INDEX idx_after_status (after_status),
    INDEX idx_change_type (change_type),
    INDEX idx_change_person_id (change_person_id),
    INDEX idx_change_time (change_time),
    INDEX idx_related_task_id (related_task_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待状态变更历史表';
```

#### 7. 接待审批意见表 (reception_approval_opinion)

```sql
CREATE TABLE reception_approval_opinion (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    approval_level TINYINT NOT NULL COMMENT '审批级别：1-部门主管审批 2-综管部审批 3-财务审批',
    approval_sequence INT COMMENT '审批序号（同级多人审批时使用）',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审批人姓名',
    approver_dept_id BIGINT COMMENT '审批人部门ID',
    approver_dept_name VARCHAR(100) COMMENT '审批人部门名称',
    approver_role VARCHAR(50) COMMENT '审批人角色',
    approval_type TINYINT NOT NULL COMMENT '审批类型：1-单人审批 2-会签 3-或签 4-依次审批',
    approval_result TINYINT NOT NULL COMMENT '审批结果：1-通过 2-驳回 3-退回修改 4-转交他人 5-加签',
    approval_time DATETIME NOT NULL COMMENT '审批时间',
    approval_duration INT COMMENT '审批耗时（分钟）',
    opinion_type TINYINT COMMENT '意见类型：1-同意 2-有条件同意 3-不同意 4-建议修改',
    opinion_content TEXT COMMENT '审批意见内容',
    opinion_summary VARCHAR(200) COMMENT '意见摘要',
    conditions_requirements TEXT COMMENT '附加条件或要求',
    cost_approval_amount DECIMAL(10,2) COMMENT '费用审批金额',
    resource_allocation_suggestion VARCHAR(200) COMMENT '资源分配建议',
    service_level_suggestion TINYINT COMMENT '服务级别建议',
    attachment_urls JSON COMMENT '审批附件URL数组',
    signature_image VARCHAR(500) COMMENT '电子签名图片URL',
    delegate_person_id BIGINT COMMENT '被委托人ID（转交时使用）',
    delegate_reason VARCHAR(200) COMMENT '委托原因',
    is_final_approval TINYINT DEFAULT 0 COMMENT '是否为最终审批：0-否 1-是',
    next_approver_suggestion VARCHAR(200) COMMENT '下级审批建议',
    approval_weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '审批权重（会签时使用）',
    ip_address VARCHAR(50) COMMENT '审批IP地址',
    device_info VARCHAR(200) COMMENT '审批设备信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_approval_level (approval_level),
    INDEX idx_approver_id (approver_id),
    INDEX idx_approval_result (approval_result),
    INDEX idx_approval_time (approval_time),
    INDEX idx_is_final_approval (is_final_approval),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待审批意见表';
```

#### 8. 接待费用记录表 (reception_cost_record)

```sql
CREATE TABLE reception_cost_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    cost_no VARCHAR(50) NOT NULL COMMENT '费用单号',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    cost_type TINYINT NOT NULL COMMENT '费用类型：1-住宿费 2-餐费 3-服务费 4-其他费用',
    cost_category VARCHAR(50) COMMENT '费用分类',

    -- 费用信息
    cost_amount DECIMAL(10,2) NOT NULL COMMENT '费用金额',
    cost_date DATE NOT NULL COMMENT '费用发生日期',
    cost_description VARCHAR(200) COMMENT '费用描述',
    unit_price DECIMAL(8,2) COMMENT '单价',
    quantity INT COMMENT '数量',

    -- 关联信息
    accommodation_id BIGINT COMMENT '关联住宿安排ID',
    dining_id BIGINT COMMENT '关联就餐安排ID',

    -- 结算信息
    settlement_status TINYINT DEFAULT 1 COMMENT '结算状态：1-待结算 2-已结算 3-已退款 4-无需结算',
    settlement_time DATETIME COMMENT '结算时间',
    settlement_person_id BIGINT COMMENT '结算人ID',
    settlement_person_name VARCHAR(50) COMMENT '结算人姓名',

    -- 发票信息
    invoice_required TINYINT DEFAULT 0 COMMENT '是否需要发票：0-否 1-是',
    invoice_no VARCHAR(50) COMMENT '发票号码',
    invoice_date DATE COMMENT '发票日期',
    invoice_amount DECIMAL(10,2) COMMENT '发票金额',
    invoice_url VARCHAR(500) COMMENT '发票图片URL',

    -- 支付信息
    payment_method TINYINT COMMENT '支付方式：1-现金 2-转账 3-公司承担 4-部门承担',
    payment_status TINYINT DEFAULT 1 COMMENT '支付状态：1-待支付 2-已支付 3-已退款',
    payment_time DATETIME COMMENT '支付时间',
    transaction_no VARCHAR(50) COMMENT '交易流水号',

    remarks VARCHAR(500) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_cost_no (cost_no),
    INDEX idx_application_id (application_id),
    INDEX idx_cost_type (cost_type),
    INDEX idx_cost_date (cost_date),
    INDEX idx_settlement_status (settlement_status),
    INDEX idx_accommodation_id (accommodation_id),
    INDEX idx_dining_id (dining_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待费用记录表';
```

#### 9. 接待流程实例扩展表 (reception_process_instance_ext)

```sql
CREATE TABLE reception_process_instance_ext (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '接待申请ID',
    process_instance_id VARCHAR(64) NOT NULL COMMENT 'Flowable流程实例ID',
    process_definition_id VARCHAR(64) COMMENT '流程定义ID',
    process_definition_key VARCHAR(100) COMMENT '流程定义Key',
    business_key VARCHAR(100) COMMENT '业务Key（申请单号）',
    process_name VARCHAR(100) COMMENT '流程名称',
    process_status TINYINT NOT NULL COMMENT '流程状态：1-运行中 2-已完成 3-已终止 4-已挂起',
    start_time DATETIME NOT NULL COMMENT '流程开始时间',
    end_time DATETIME COMMENT '流程结束时间',
    duration_minutes INT COMMENT '流程耗时（分钟）',
    current_task_id VARCHAR(64) COMMENT '当前任务ID',
    current_task_name VARCHAR(100) COMMENT '当前任务名称',
    current_assignee_id BIGINT COMMENT '当前处理人ID',
    current_assignee_name VARCHAR(50) COMMENT '当前处理人姓名',

    -- SLA管理
    sla_deadline DATETIME COMMENT 'SLA截止时间',
    sla_status TINYINT DEFAULT 1 COMMENT 'SLA状态：1-正常 2-即将超时 3-已超时',
    sla_warning_sent TINYINT DEFAULT 0 COMMENT 'SLA预警是否已发送：0-否 1-是',

    -- 流程控制
    priority_level TINYINT DEFAULT 2 COMMENT '优先级：1-高 2-中 3-低',
    escalation_count INT DEFAULT 0 COMMENT '升级次数',
    last_escalation_time DATETIME COMMENT '最后升级时间',
    auto_approval_enabled TINYINT DEFAULT 0 COMMENT '是否启用自动审批：0-否 1-是',

    -- 业务扩展
    reception_type TINYINT COMMENT '接待类型',
    reception_level TINYINT COMMENT '接待级别',
    estimated_cost DECIMAL(10,2) COMMENT '预估费用',
    visitor_count INT COMMENT '访客人数',

    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_process_instance_id (process_instance_id),
    UNIQUE KEY uk_application_id (application_id),
    INDEX idx_business_key (business_key),
    INDEX idx_process_status (process_status),
    INDEX idx_sla_status (sla_status),
    INDEX idx_start_time (start_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接待流程实例扩展表';
```

### 表关联关系说明

#### 1. 业务表关联关系图

##### A. 核心业务表关联关系图

```mermaid
graph TB
    subgraph "接待申请核心"
        RA[reception_application<br/>接待申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>visitor_application_id: 关联访客申请<br/>reception_type: 接待类型<br/>status: 状态]

        RPIE[reception_process_instance_ext<br/>流程实例扩展表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>process_instance_id: 流程实例ID<br/>business_key: 业务Key<br/>process_status: 流程状态]
    end

    subgraph "服务安排数据"
        RAC[reception_accommodation<br/>住宿安排表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>room_id: 房间ID<br/>check_in_date: 入住日期<br/>status: 状态]

        RD[reception_dining<br/>就餐安排表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>meal_standard: 用餐标准<br/>meal_card_no: 饭卡号<br/>status: 状态]

        RDR[reception_dining_record<br/>就餐记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>dining_id: 就餐安排ID<br/>dining_date: 就餐日期<br/>meal_type: 餐次类型<br/>total_amount: 消费金额]
    end

    subgraph "流程记录数据"
        RPO[reception_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>operation_type: 操作类型<br/>operator_id: 操作人<br/>operation_time: 操作时间]

        RAO[reception_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>approval_level: 审批级别<br/>approval_result: 审批结果<br/>opinion_content: 审批意见]

        RSH[reception_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态<br/>change_time: 变更时间]

        RCR[reception_cost_record<br/>费用记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>cost_type: 费用类型<br/>cost_amount: 费用金额<br/>settlement_status: 结算状态]
    end

    %% 关联关系
    RA -->|1:1<br/>id = application_id| RPIE
    RA -->|1:N<br/>id = application_id| RAC
    RA -->|1:N<br/>id = application_id| RD
    RA -->|1:N<br/>id = application_id| RPO
    RA -->|1:N<br/>id = application_id| RAO
    RA -->|1:N<br/>id = application_id| RSH
    RA -->|1:N<br/>id = application_id| RCR

    RD -->|1:N<br/>id = dining_id| RDR
    RAC -->|1:N<br/>id = accommodation_id| RCR
    RD -->|1:N<br/>id = dining_id| RCR

    RPO -.->|1:1<br/>关联操作| RSH

    style RA fill:#e3f2fd
    style RPIE fill:#e3f2fd
    style RAC fill:#e8f5e8
    style RD fill:#e8f5e8
    style RDR fill:#e8f5e8
    style RPO fill:#f3e5f5
    style RAO fill:#f3e5f5
    style RSH fill:#f3e5f5
    style RCR fill:#fff3e0
```

##### B. 数据流向关系图

```mermaid
graph LR
    subgraph "数据输入"
        UI[用户界面]
        API[API接口]
        WX[微信小程序]
        SYS[系统自动]
    end

    subgraph "核心业务流程"
        APP[接待申请]
        APPROVAL[审批流程]
        ALLOCATION[资源分配]
        SERVICE[服务执行]
        SETTLEMENT[费用结算]
    end

    subgraph "数据存储"
        MASTER[主数据表]
        PROCESS[流程记录表]
        BUSINESS[业务记录表]
    end

    subgraph "数据输出"
        REPORT[报表统计]
        NOTIFY[消息通知]
        EXPORT[数据导出]
        BILLING[账单生成]
    end

    UI --> APP
    API --> APP
    WX --> APP
    SYS --> ALLOCATION

    APP --> APPROVAL
    APPROVAL --> ALLOCATION
    ALLOCATION --> SERVICE
    SERVICE --> SETTLEMENT

    APP --> MASTER
    APPROVAL --> PROCESS
    ALLOCATION --> PROCESS
    SERVICE --> BUSINESS
    SETTLEMENT --> BUSINESS

    MASTER --> REPORT
    PROCESS --> REPORT
    BUSINESS --> REPORT

    PROCESS --> NOTIFY
    BUSINESS --> NOTIFY

    MASTER --> EXPORT
    BUSINESS --> EXPORT
    BUSINESS --> BILLING

    style UI fill:#ffebee
    style API fill:#ffebee
    style WX fill:#ffebee
    style SYS fill:#ffebee
    style REPORT fill:#e8f5e8
    style NOTIFY fill:#e8f5e8
    style EXPORT fill:#e8f5e8
    style BILLING fill:#e8f5e8
```

#### 2. 业务表与Flowable工作流表关系图

##### A. 流程实例级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        RA[reception_application<br/>接待申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>process_instance_id: 流程实例ID<br/>status: 申请状态]

        RPIE[reception_process_instance_ext<br/>流程实例扩展表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>process_instance_id: 流程实例ID<br/>business_key: 业务Key<br/>process_status: 流程状态]
    end

    subgraph "Flowable工作流层"
        ARE[ACT_RU_EXECUTION<br/>运行时流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>ACT_ID_: 当前活动ID<br/>START_USER_ID_: 发起人]

        AHP[ACT_HI_PROCINST<br/>历史流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    RA -.->|1:1<br/>process_instance_id| ARE
    RPIE -.->|1:1<br/>process_instance_id| AHP
    RA -->|1:1<br/>id = application_id| RPIE

    style RA fill:#e1f5fe
    style RPIE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

##### B. 任务级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        RAO[reception_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>approver_id: 审批人<br/>approval_result: 审批结果]

        RPO[reception_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>operation_type: 操作类型<br/>operator_id: 操作人]

        RSH[reception_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>related_task_id: 关联任务ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态]
    end

    subgraph "Flowable工作流层"
        ART[ACT_RU_TASK<br/>运行时任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>TASK_DEF_KEY_: 任务定义Key<br/>ASSIGNEE_: 处理人]

        AHT[ACT_HI_TASKINST<br/>历史任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    RAO -.->|N:1<br/>task_id = ID_| ART
    RPO -.->|N:1<br/>task_id = ID_| AHT
    RSH -.->|N:1<br/>related_task_id = ID_| AHT

    style RAO fill:#e8f5e8
    style RPO fill:#e8f5e8
    style RSH fill:#e8f5e8
    style ART fill:#fff3e0
    style AHT fill:#fff3e0
```

##### C. 业务Key关联关系图

```mermaid
graph LR
    subgraph "业务标识"
        AN[application_no<br/>申请单号<br/>格式: RA20250111001]
    end

    subgraph "业务表"
        RA[reception_application<br/>application_no]
        RPIE[reception_process_instance_ext<br/>business_key]
    end

    subgraph "Flowable表"
        ARE[ACT_RU_EXECUTION<br/>BUSINESS_KEY_]
        AHP[ACT_HI_PROCINST<br/>BUSINESS_KEY_]
    end

    AN --> RA
    AN --> RPIE
    AN --> ARE
    AN --> AHP

    style AN fill:#ffebee
    style RA fill:#e1f5fe
    style RPIE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

#### 3. 关联关系详细说明

**A. 流程实例级别关联**

**1. reception_application 与 ACT_RU_EXECUTION 的关联关系**

这是接待申请表与Flowable运行时流程实例表之间的核心关联关系。当用户提交接待申请并启动审批流程时，系统会在Flowable引擎中创建一个流程实例，该实例的ID会存储在`reception_application.process_instance_id`字段中。

- **关联字段映射**：`reception_application.process_instance_id` = `ACT_RU_EXECUTION.PROC_INST_ID_`
- **关系类型**：1:1（一个接待申请对应一个流程实例）
- **业务含义**：通过此关联可以查询接待申请当前的流程状态、当前处理节点、处理人等信息
- **数据流向**：业务操作触发流程引擎，流程状态变化反映到业务数据
- **查询场景**：查询某个申请的当前审批状态、查询某个流程实例对应的业务数据

**2. reception_process_instance_ext 与 ACT_HI_PROCINST 的关联关系**

这是业务流程扩展表与Flowable历史流程实例表之间的映射关系。扩展表存储了业务相关的流程信息，与Flowable的历史表形成互补。

- **关联字段映射**：`reception_process_instance_ext.process_instance_id` = `ACT_HI_PROCINST.PROC_INST_ID_`
- **关系类型**：1:1（一个业务流程扩展记录对应一个历史流程实例）
- **业务含义**：扩展表补充了Flowable历史表中缺少的业务信息，如SLA状态、升级次数、业务优先级等
- **数据同步**：流程启动时同时创建两个表的记录，流程状态变化时同步更新
- **查询场景**：流程历史分析、SLA监控、流程效率统计

**B. 任务级别关联**

**1. reception_approval_opinion 与 ACT_RU_TASK 的关联关系**

这是审批意见表与Flowable运行时任务表之间的关联关系。每当流程流转到需要人工审批的节点时，Flowable会创建一个任务，审批人完成审批后，系统会在审批意见表中记录详细的审批信息。

- **关联字段映射**：`reception_approval_opinion.task_id` = `ACT_RU_TASK.ID_`
- **关系类型**：N:1（一个任务可能有多条审批意见，如会签场景）
- **业务含义**：记录每个审批任务的详细审批意见、附件、风险评估等业务信息
- **数据流向**：任务创建时获取任务信息，任务完成时记录审批结果
- **查询场景**：查询某个任务的审批意见、统计审批人的审批历史

**2. reception_process_operation 与 ACT_HI_TASKINST 的关联关系**

这是流程操作记录表与Flowable历史任务表之间的关联关系。系统会记录每个任务相关的所有操作，包括任务分配、处理、完成等操作。

- **关联字段映射**：`reception_process_operation.task_id` = `ACT_HI_TASKINST.ID_`
- **关系类型**：N:1（一个任务可能有多个操作记录，如分配、处理、完成）
- **业务含义**：记录任务生命周期中的所有操作，包括系统自动操作和人工操作
- **数据流向**：任务状态变化时实时记录操作信息
- **查询场景**：审计追踪、操作历史查询、性能分析

**C. 业务Key关联**

**业务Key的作用和映射关系**

业务Key是连接业务数据和工作流数据的重要桥梁，它提供了一种通过业务标识快速定位流程实例的方式。

**1. reception_application.application_no → ACT_RU_EXECUTION.BUSINESS_KEY_**

- **映射关系**：接待申请单号直接作为Flowable流程实例的业务Key
- **业务含义**：通过申请单号可以快速定位到对应的流程实例
- **数据同步**：流程启动时将申请单号设置为业务Key
- **查询优势**：支持通过申请单号快速查询流程状态，无需知道流程实例ID
- **使用场景**：
  - 接待管理人员通过申请单号查询审批进度
  - 系统集成时通过业务单号进行数据交换
  - 异常处理时通过申请单号定位问题流程

#### 4. 数据一致性保证

**应用层数据一致性控制：**
- 所有表均不使用数据库外键约束，通过应用层逻辑维护数据一致性
- 使用`@Transactional`注解确保业务操作和相关记录在同一事务中执行
- 异常情况下自动回滚所有相关记录，避免数据不一致

**数据完整性验证：**
- 在Service层进行关联数据的存在性验证
- 删除主记录前检查是否存在关联的子记录
- 提供数据完整性检查工具，定期验证数据关联关系

**级联操作处理：**
- 在应用层实现逻辑删除，避免物理删除导致的数据丢失
- 删除接待申请时，同步更新所有关联记录的状态
- 提供批量数据清理功能，定期清理过期的无效数据

**审计追踪：**
- 所有数据变更操作都有完整的审计记录
- 支持按时间、操作人、操作类型等维度查询
- 提供流程回溯和问题定位功能
- 记录数据一致性检查和修复的操作日志

**外部系统数据同步：**
- 与住宿管理系统的房间数据同步机制
- 与饭卡系统的消费记录同步机制
- 与人脸识别系统的人脸数据同步机制
- 提供数据同步失败的重试和告警机制
```

## API接口设计

### 接待申请管理接口

#### 1. 接待申请创建接口

**接口地址：** `POST /admin-api/reception/application/create`

**请求参数：**

```java
@Data
@ApiModel("接待申请创建 Request VO")
public class ReceptionApplicationCreateReqVO {

    @ApiModelProperty(value = "关联访客申请ID", required = true, example = "1")
    @NotNull(message = "关联访客申请ID不能为空")
    private Long visitorApplicationId;

    @ApiModelProperty(value = "关联访客申请单号", required = true, example = "VA20250111001")
    @NotBlank(message = "关联访客申请单号不能为空")
    private String visitorApplicationNo;

    @ApiModelProperty(value = "访客姓名", required = true, example = "张三")
    @NotBlank(message = "访客姓名不能为空")
    @Length(max = 50, message = "访客姓名长度不能超过50个字符")
    private String visitorName;

    @ApiModelProperty(value = "访客电话", required = true, example = "13800138000")
    @NotBlank(message = "访客电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "访客电话格式不正确")
    private String visitorPhone;

    @ApiModelProperty(value = "访客单位", example = "ABC公司")
    @Length(max = 100, message = "访客单位长度不能超过100个字符")
    private String visitorCompany;

    @ApiModelProperty(value = "访客人数", example = "2")
    @Min(value = 1, message = "访客人数不能少于1人")
    @Max(value = 20, message = "访客人数不能超过20人")
    private Integer visitorCount;

    @ApiModelProperty(value = "访客名单", example = "[{\"name\":\"张三\",\"phone\":\"13800138000\"},{\"name\":\"李四\",\"phone\":\"13900139000\"}]")
    private String visitorList;

    @ApiModelProperty(value = "接待类型", required = true, example = "3", notes = "1-仅住宿 2-仅就餐 3-住宿+就餐")
    @NotNull(message = "接待类型不能为空")
    @InEnum(ReceptionTypeEnum.class, message = "接待类型必须是 {value}")
    private Integer receptionType;

    @ApiModelProperty(value = "接待级别", example = "2", notes = "1-普通 2-重要 3-VIP")
    @InEnum(ReceptionLevelEnum.class, message = "接待级别必须是 {value}")
    private Integer receptionLevel;

    @ApiModelProperty(value = "接待原因", example = "重要客户商务洽谈")
    @Length(max = 200, message = "接待原因长度不能超过200个字符")
    private String receptionReason;

    // 住宿相关字段
    @ApiModelProperty(value = "是否需要住宿", example = "1", notes = "0-否 1-是")
    private Integer needAccommodation;

    @ApiModelProperty(value = "入住日期", example = "2025-01-15")
    @Future(message = "入住日期必须是未来日期")
    private LocalDate checkInDate;

    @ApiModelProperty(value = "退宿日期", example = "2025-01-17")
    @Future(message = "退宿日期必须是未来日期")
    private LocalDate checkOutDate;

    @ApiModelProperty(value = "房间类型", example = "2", notes = "1-标准间 2-单人间 3-套房")
    private Integer roomType;

    @ApiModelProperty(value = "房间数量", example = "1")
    @Min(value = 1, message = "房间数量不能少于1间")
    @Max(value = 10, message = "房间数量不能超过10间")
    private Integer roomCount;

    @ApiModelProperty(value = "特殊要求", example = "需要无烟房间")
    @Length(max = 500, message = "特殊要求长度不能超过500个字符")
    private String specialRequirements;

    // 就餐相关字段
    @ApiModelProperty(value = "是否需要就餐", example = "1", notes = "0-否 1-是")
    private Integer needDining;

    @ApiModelProperty(value = "就餐开始日期", example = "2025-01-15")
    private LocalDate diningStartDate;

    @ApiModelProperty(value = "就餐结束日期", example = "2025-01-17")
    private LocalDate diningEndDate;

    @ApiModelProperty(value = "用餐标准", example = "2", notes = "1-普通 2-商务 3-高级")
    private Integer mealStandard;

    @ApiModelProperty(value = "早餐人次", example = "4")
    @Min(value = 0, message = "早餐人次不能为负数")
    private Integer breakfastCount;

    @ApiModelProperty(value = "午餐人次", example = "6")
    @Min(value = 0, message = "午餐人次不能为负数")
    private Integer lunchCount;

    @ApiModelProperty(value = "晚餐人次", example = "4")
    @Min(value = 0, message = "晚餐人次不能为负数")
    private Integer dinnerCount;

    @ApiModelProperty(value = "费用承担方", example = "1", notes = "1-公司承担 2-访客自付 3-部门承担")
    private Integer costBearer;

}
```

**响应参数：**

```java
@Data
@ApiModel("接待申请创建 Response VO")
public class ReceptionApplicationCreateRespVO {

    @ApiModelProperty(value = "申请ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "申请单号", example = "RA20250111001")
    private String applicationNo;

    @ApiModelProperty(value = "流程实例ID", example = "12345")
    private String processInstanceId;

    @ApiModelProperty(value = "申请状态", example = "1", notes = "1-待审批 2-已审批 3-已驳回")
    private Integer status;

    @ApiModelProperty(value = "预估总费用", example = "1200.00")
    private BigDecimal estimatedTotalCost;

    @ApiModelProperty(value = "申请时间", example = "2025-01-11 10:30:00")
    private LocalDateTime createTime;

}
```

**接口实现：**

```java
@RestController
@RequestMapping("/admin-api/reception/application")
@Tag(name = "管理后台 - 接待申请")
@Validated
public class ReceptionApplicationController {

    @Resource
    private ReceptionApplicationService receptionApplicationService;

    @PostMapping("/create")
    @Operation(summary = "创建接待申请")
    @PreAuthorize("@ss.hasPermission('reception:application:create')")
    public CommonResult<ReceptionApplicationCreateRespVO> createApplication(@Valid @RequestBody ReceptionApplicationCreateReqVO createReqVO) {
        Long id = receptionApplicationService.createApplication(createReqVO);
        return success(receptionApplicationService.getApplicationCreateResp(id));
    }

    @PutMapping("/update")
    @Operation(summary = "更新接待申请")
    @PreAuthorize("@ss.hasPermission('reception:application:update')")
    public CommonResult<Boolean> updateApplication(@Valid @RequestBody ReceptionApplicationUpdateReqVO updateReqVO) {
        receptionApplicationService.updateApplication(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除接待申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('reception:application:delete')")
    public CommonResult<Boolean> deleteApplication(@RequestParam("id") Long id) {
        receptionApplicationService.deleteApplication(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得接待申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('reception:application:query')")
    public CommonResult<ReceptionApplicationRespVO> getApplication(@RequestParam("id") Long id) {
        ReceptionApplicationDO application = receptionApplicationService.getApplication(id);
        return success(ReceptionApplicationConvert.INSTANCE.convert(application));
    }

    @GetMapping("/page")
    @Operation(summary = "获得接待申请分页")
    @PreAuthorize("@ss.hasPermission('reception:application:query')")
    public CommonResult<PageResult<ReceptionApplicationRespVO>> getApplicationPage(@Valid ReceptionApplicationPageReqVO pageReqVO) {
        PageResult<ReceptionApplicationDO> pageResult = receptionApplicationService.getApplicationPage(pageReqVO);
        return success(ReceptionApplicationConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/approve")
    @Operation(summary = "审批接待申请")
    @PreAuthorize("@ss.hasPermission('reception:application:approve')")
    public CommonResult<Boolean> approveApplication(@Valid @RequestBody ReceptionApplicationApproveReqVO approveReqVO) {
        receptionApplicationService.approveApplication(approveReqVO);
        return success(true);
    }

    @PostMapping("/allocate-resources")
    @Operation(summary = "分配接待资源")
    @PreAuthorize("@ss.hasPermission('reception:application:allocate')")
    public CommonResult<Boolean> allocateResources(@Valid @RequestBody ReceptionResourceAllocateReqVO allocateReqVO) {
        receptionApplicationService.allocateResources(allocateReqVO);
        return success(true);
    }

}
```

## 业务流程设计

### 核心业务流程图

#### 1. 接待申请审批流程

```mermaid
graph TD
    A[访客申请已审批] --> B[提交接待申请]
    B --> C{申请信息校验}
    C -->|校验失败| D[返回修改]
    C -->|校验通过| E[生成申请单号]
    E --> F[启动审批流程]

    F --> G[部门主管审批]
    G --> H{审批结果}
    H -->|驳回| I[申请驳回]
    H -->|通过| J[综管部审批]

    J --> K{审批结果}
    K -->|驳回| I
    K -->|通过| L{费用审批}

    L -->|需要财务审批| M[财务部审批]
    M --> N{审批结果}
    N -->|驳回| I
    N -->|通过| O[资源分配]

    L -->|无需财务审批| O
    O --> P{资源分配结果}
    P -->|分配失败| Q[资源不足通知]
    P -->|分配成功| R[服务安排完成]

    R --> S[发送确认通知]
    S --> T[等待服务执行]

    I --> U[流程结束]
    Q --> U
    T --> V[服务开始]
    V --> W[服务进行中]
    W --> X[服务完成]
    X --> Y[费用结算]
    Y --> Z[流程完成]
    Z --> U
```

#### 2. 住宿服务流程

```mermaid
graph TD
    A[接待申请审批通过] --> B{需要住宿服务}
    B -->|否| C[跳过住宿安排]
    B -->|是| D[查询可用房间]

    D --> E{房间可用性}
    E -->|无可用房间| F[通知申请人]
    E -->|有可用房间| G[分配房间]

    G --> H[生成住宿安排单]
    H --> I[发送入住通知]
    I --> J[访客到达]

    J --> K[办理入住手续]
    K --> L[房卡发放]
    L --> M[入住登记]
    M --> N[住宿服务中]

    N --> O[访客退宿]
    O --> P[房卡回收]
    P --> Q[房间检查]
    Q --> R[费用结算]
    R --> S[退宿登记]
    S --> T[住宿服务完成]

    F --> U[流程结束]
    C --> U
    T --> U
```

#### 3. 就餐服务流程

```mermaid
graph TD
    A[接待申请审批通过] --> B{需要就餐服务}
    B -->|否| C[跳过就餐安排]
    B -->|是| D[生成就餐安排单]

    D --> E{饭卡系统对接}
    E -->|使用饭卡| F[申领饭卡]
    E -->|人脸识别| G[录入人脸信息]

    F --> H[饭卡发放]
    G --> I[人脸注册完成]
    H --> J[就餐服务开始]
    I --> J

    J --> K[访客就餐]
    K --> L[消费记录]
    L --> M{继续就餐}
    M -->|是| K
    M -->|否| N[就餐服务结束]

    N --> O{使用饭卡}
    O -->|是| P[饭卡回收]
    O -->|否| Q[费用结算]
    P --> Q
    Q --> R[就餐服务完成]

    C --> S[流程结束]
    R --> S
```

### 异常场景处理决策树

#### 1. 资源分配异常处理

```mermaid
graph TD
    A[资源分配开始] --> B{房间资源检查}
    B -->|房间充足| C[分配房间]
    B -->|房间不足| D{是否VIP接待}

    D -->|是| E[释放低优先级房间]
    D -->|否| F[通知申请人延期]

    E --> G{释放成功}
    G -->|是| C
    G -->|否| H[升级房间类型]

    H --> I{升级成功}
    I -->|是| C
    I -->|否| J[联系外部酒店]

    J --> K{外部酒店可用}
    K -->|是| L[安排外部住宿]
    K -->|否| M[申请驳回]

    C --> N[分配成功]
    F --> O[等待重新分配]
    L --> P[外部住宿安排]
    M --> Q[流程终止]

    O --> R{重新分配时机}
    R -->|有房间释放| A
    R -->|超时| M
```

#### 2. 费用超标处理决策树

```mermaid
graph TD
    A[费用计算完成] --> B{费用是否超标}
    B -->|未超标| C[正常审批流程]
    B -->|超标| D{超标幅度}

    D -->|超标20%以内| E[部门主管特批]
    D -->|超标20%-50%| F[综管部审批]
    D -->|超标50%以上| G[总经理审批]

    E --> H{特批结果}
    H -->|通过| I[降级服务标准]
    H -->|拒绝| J[申请修改]

    F --> K{综管部审批结果}
    K -->|通过| L[保持原标准]
    K -->|拒绝| I

    G --> M{总经理审批结果}
    M -->|通过| L
    M -->|拒绝| N[申请驳回]

    I --> O[重新计算费用]
    J --> P[申请人修改]
    L --> Q[继续审批流程]
    N --> R[流程终止]

    O --> S{费用是否合理}
    S -->|是| Q
    S -->|否| P

    P --> T{修改完成}
    T -->|是| A
    T -->|否| R
```

### 角色权限矩阵

| 角色/权限 | 申请提交 | 申请查看 | 申请修改 | 部门审批 | 综管审批 | 财务审批 | 资源分配 | 服务执行 | 费用结算 |
|-----------|----------|----------|----------|----------|----------|----------|----------|----------|----------|
| **申请人** | ✓ | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ |
| **联系人** | ✓ | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ |
| **部门主管** | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ |
| **综管部负责人** | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ | ✓ | ✗ | ✗ |
| **财务人员** | ✗ | ✓ | ✗ | ✗ | ✗ | ✓ | ✗ | ✗ | ✓ |
| **宿舍管理员** | ✗ | ✓ | ✗ | ✗ | ✗ | ✗ | ✓ | ✓ | ✗ |
| **餐厅管理员** | ✗ | ✓ | ✗ | ✗ | ✗ | ✗ | ✓ | ✓ | ✗ |
| **系统管理员** | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |

### 业务规则详细算法

#### 1. 接待申请验证算法

```java
/**
 * 接待申请数据验证算法
 */
public class ReceptionApplicationValidator {

    /**
     * 验证接待申请数据
     */
    public ValidationResult validateApplication(ReceptionApplicationCreateReqVO reqVO) {
        ValidationResult result = new ValidationResult();

        // 1. 基础信息验证
        if (!validateBasicInfo(reqVO, result)) {
            return result;
        }

        // 2. 访客申请关联验证
        if (!validateVisitorApplication(reqVO, result)) {
            return result;
        }

        // 3. 时间逻辑验证
        if (!validateTimeLogic(reqVO, result)) {
            return result;
        }

        // 4. 资源可用性验证
        if (!validateResourceAvailability(reqVO, result)) {
            return result;
        }

        // 5. 费用合理性验证
        if (!validateCostReasonableness(reqVO, result)) {
            return result;
        }

        result.setValid(true);
        return result;
    }

    /**
     * 验证基础信息
     */
    private boolean validateBasicInfo(ReceptionApplicationCreateReqVO reqVO, ValidationResult result) {
        // 访客人数验证
        if (reqVO.getVisitorCount() == null || reqVO.getVisitorCount() < 1) {
            result.addError("访客人数不能少于1人");
            return false;
        }

        if (reqVO.getVisitorCount() > 20) {
            result.addError("访客人数不能超过20人");
            return false;
        }

        // 接待类型验证
        if (reqVO.getReceptionType() == null) {
            result.addError("接待类型不能为空");
            return false;
        }

        // 住宿和就餐至少选择一项
        boolean needAccommodation = reqVO.getNeedAccommodation() != null && reqVO.getNeedAccommodation() == 1;
        boolean needDining = reqVO.getNeedDining() != null && reqVO.getNeedDining() == 1;

        if (!needAccommodation && !needDining) {
            result.addError("住宿和就餐服务至少选择一项");
            return false;
        }

        return true;
    }

    /**
     * 验证访客申请关联
     */
    private boolean validateVisitorApplication(ReceptionApplicationCreateReqVO reqVO, ValidationResult result) {
        // 检查访客申请是否存在
        VisitorApplicationDO visitorApplication = visitorApplicationService.getByApplicationNo(reqVO.getVisitorApplicationNo());
        if (visitorApplication == null) {
            result.addError("关联的访客申请不存在");
            return false;
        }

        // 检查访客申请状态
        if (!VisitorStatusEnum.APPROVED.getStatus().equals(visitorApplication.getStatus())) {
            result.addError("关联的访客申请尚未审批通过");
            return false;
        }

        // 检查是否已有接待申请
        ReceptionApplicationDO existingReception = receptionApplicationService.getByVisitorApplicationId(visitorApplication.getId());
        if (existingReception != null) {
            result.addError("该访客申请已有对应的接待申请");
            return false;
        }

        return true;
    }

    /**
     * 验证时间逻辑
     */
    private boolean validateTimeLogic(ReceptionApplicationCreateReqVO reqVO, ValidationResult result) {
        LocalDate now = LocalDate.now();

        // 住宿时间验证
        if (reqVO.getNeedAccommodation() != null && reqVO.getNeedAccommodation() == 1) {
            if (reqVO.getCheckInDate() == null) {
                result.addError("需要住宿时入住日期不能为空");
                return false;
            }

            if (reqVO.getCheckOutDate() == null) {
                result.addError("需要住宿时退宿日期不能为空");
                return false;
            }

            if (reqVO.getCheckInDate().isBefore(now)) {
                result.addError("入住日期不能早于当前日期");
                return false;
            }

            if (reqVO.getCheckOutDate().isBefore(reqVO.getCheckInDate()) ||
                reqVO.getCheckOutDate().equals(reqVO.getCheckInDate())) {
                result.addError("退宿日期必须晚于入住日期");
                return false;
            }

            long accommodationDays = ChronoUnit.DAYS.between(reqVO.getCheckInDate(), reqVO.getCheckOutDate());
            if (accommodationDays > 30) {
                result.addError("住宿时长不能超过30天");
                return false;
            }
        }

        // 就餐时间验证
        if (reqVO.getNeedDining() != null && reqVO.getNeedDining() == 1) {
            if (reqVO.getDiningStartDate() == null) {
                result.addError("需要就餐时就餐开始日期不能为空");
                return false;
            }

            if (reqVO.getDiningEndDate() == null) {
                result.addError("需要就餐时就餐结束日期不能为空");
                return false;
            }

            if (reqVO.getDiningStartDate().isBefore(now)) {
                result.addError("就餐开始日期不能早于当前日期");
                return false;
            }

            if (reqVO.getDiningEndDate().isBefore(reqVO.getDiningStartDate())) {
                result.addError("就餐结束日期不能早于开始日期");
                return false;
            }
        }

        return true;
    }

    /**
     * 验证资源可用性
     */
    private boolean validateResourceAvailability(ReceptionApplicationCreateReqVO reqVO, ValidationResult result) {
        // 房间资源验证
        if (reqVO.getNeedAccommodation() != null && reqVO.getNeedAccommodation() == 1) {
            int availableRooms = accommodationService.getAvailableRoomCount(
                reqVO.getRoomType(), reqVO.getCheckInDate(), reqVO.getCheckOutDate());

            if (availableRooms < reqVO.getRoomCount()) {
                result.addError(String.format("所选时间段内%s类型房间不足，可用%d间，需要%d间",
                    getRoomTypeName(reqVO.getRoomType()), availableRooms, reqVO.getRoomCount()));
                return false;
            }
        }

        // 餐厅容量验证
        if (reqVO.getNeedDining() != null && reqVO.getNeedDining() == 1) {
            int maxDailyCapacity = diningService.getMaxDailyCapacity();
            int totalMealCount = (reqVO.getBreakfastCount() != null ? reqVO.getBreakfastCount() : 0) +
                               (reqVO.getLunchCount() != null ? reqVO.getLunchCount() : 0) +
                               (reqVO.getDinnerCount() != null ? reqVO.getDinnerCount() : 0);

            if (totalMealCount > maxDailyCapacity) {
                result.addError(String.format("就餐人次超过餐厅日接待能力，最大%d人次，申请%d人次",
                    maxDailyCapacity, totalMealCount));
                return false;
            }
        }

        return true;
    }

    /**
     * 验证费用合理性
     */
    private boolean validateCostReasonableness(ReceptionApplicationCreateReqVO reqVO, ValidationResult result) {
        BigDecimal estimatedCost = calculateEstimatedCost(reqVO);

        // 获取费用标准
        BigDecimal maxCostLimit = getMaxCostLimit(reqVO.getReceptionLevel());

        if (estimatedCost.compareTo(maxCostLimit) > 0) {
            result.addWarning(String.format("预估费用%.2f元超过标准%.2f元，需要特殊审批",
                estimatedCost, maxCostLimit));
        }

        return true;
    }
}
```

## 技术实现深度细化

### 关键业务逻辑算法

#### 1. 智能资源分配算法

```java
/**
 * 智能资源分配算法
 */
@Service
@Slf4j
public class ReceptionResourceAllocationService {

    /**
     * 智能房间分配算法
     */
    public RoomAllocationResult allocateRooms(ReceptionApplicationDO application) {
        RoomAllocationResult result = new RoomAllocationResult();

        // 1. 获取房间需求
        RoomRequirement requirement = buildRoomRequirement(application);

        // 2. 查询可用房间
        List<AccommodationRoomDO> availableRooms = getAvailableRooms(requirement);

        // 3. 房间评分和排序
        List<RoomScore> roomScores = calculateRoomScores(availableRooms, requirement);

        // 4. 执行分配算法
        List<AccommodationRoomDO> allocatedRooms = executeAllocationAlgorithm(roomScores, requirement);

        // 5. 验证分配结果
        if (allocatedRooms.size() >= requirement.getRoomCount()) {
            result.setSuccess(true);
            result.setAllocatedRooms(allocatedRooms);
            result.setMessage("房间分配成功");
        } else {
            result.setSuccess(false);
            result.setMessage("可用房间不足");
            // 尝试替代方案
            result = tryAlternativeAllocation(requirement, availableRooms);
        }

        return result;
    }

    /**
     * 房间评分算法
     */
    private List<RoomScore> calculateRoomScores(List<AccommodationRoomDO> rooms, RoomRequirement requirement) {
        return rooms.stream().map(room -> {
            RoomScore score = new RoomScore();
            score.setRoom(room);

            double totalScore = 0.0;

            // 1. 房间类型匹配度 (权重: 30%)
            double typeScore = calculateTypeMatchScore(room, requirement);
            totalScore += typeScore * 0.3;

            // 2. 位置便利性 (权重: 20%)
            double locationScore = calculateLocationScore(room, requirement);
            totalScore += locationScore * 0.2;

            // 3. 设施完备性 (权重: 25%)
            double facilityScore = calculateFacilityScore(room, requirement);
            totalScore += facilityScore * 0.25;

            // 4. 价格合理性 (权重: 15%)
            double priceScore = calculatePriceScore(room, requirement);
            totalScore += priceScore * 0.15;

            // 5. 历史评价 (权重: 10%)
            double ratingScore = calculateRatingScore(room);
            totalScore += ratingScore * 0.1;

            score.setScore(totalScore);
            return score;
        }).sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
          .collect(Collectors.toList());
    }

    /**
     * 房间类型匹配度计算
     */
    private double calculateTypeMatchScore(AccommodationRoomDO room, RoomRequirement requirement) {
        if (room.getRoomType().equals(requirement.getPreferredRoomType())) {
            return 100.0; // 完全匹配
        }

        // 根据房间类型层级计算匹配度
        Map<Integer, Integer> typeHierarchy = Map.of(
            1, 1, // 标准间
            2, 2, // 单人间
            3, 3  // 套房
        );

        int roomLevel = typeHierarchy.get(room.getRoomType());
        int requiredLevel = typeHierarchy.get(requirement.getPreferredRoomType());

        if (roomLevel > requiredLevel) {
            return 80.0; // 升级房型
        } else if (roomLevel < requiredLevel) {
            return 60.0; // 降级房型
        }

        return 70.0; // 其他情况
    }

    /**
     * 位置便利性计算
     */
    private double calculateLocationScore(AccommodationRoomDO room, RoomRequirement requirement) {
        double score = 70.0; // 基础分

        // 楼层偏好
        if (room.getFloorNo() >= 2 && room.getFloorNo() <= 5) {
            score += 20.0; // 理想楼层
        } else if (room.getFloorNo() == 1) {
            score += 10.0; // 一楼便利但可能嘈杂
        }

        // 朝向偏好
        if ("南".equals(room.getOrientation()) || "东南".equals(room.getOrientation())) {
            score += 10.0; // 朝向好
        }

        return Math.min(score, 100.0);
    }

    /**
     * 设施完备性计算
     */
    private double calculateFacilityScore(AccommodationRoomDO room, RoomRequirement requirement) {
        double score = 50.0; // 基础分

        // 基础设施检查
        if (room.getHasAirConditioner() == 1) score += 15.0;
        if (room.getHasWifi() == 1) score += 10.0;
        if (room.getHasWaterHeater() == 1) score += 10.0;
        if (room.getHasRefrigerator() == 1) score += 5.0;
        if (room.getHasWashingMachine() == 1) score += 5.0;
        if (room.getHasBalcony() == 1) score += 5.0;

        return Math.min(score, 100.0);
    }

    /**
     * 价格合理性计算
     */
    private double calculatePriceScore(AccommodationRoomDO room, RoomRequirement requirement) {
        BigDecimal roomPrice = room.getDailyRate();
        BigDecimal budgetLimit = requirement.getBudgetLimit();

        if (budgetLimit == null || budgetLimit.compareTo(BigDecimal.ZERO) <= 0) {
            return 70.0; // 无预算限制时给中等分
        }

        double priceRatio = roomPrice.divide(budgetLimit, 4, RoundingMode.HALF_UP).doubleValue();

        if (priceRatio <= 0.8) {
            return 100.0; // 价格很合理
        } else if (priceRatio <= 1.0) {
            return 80.0; // 价格合理
        } else if (priceRatio <= 1.2) {
            return 60.0; // 价格稍高
        } else {
            return 30.0; // 价格过高
        }
    }

    /**
     * 历史评价计算
     */
    private double calculateRatingScore(AccommodationRoomDO room) {
        // 获取房间历史评分
        Double avgRating = accommodationService.getRoomAverageRating(room.getId());
        if (avgRating == null) {
            return 70.0; // 无评价时给中等分
        }

        return avgRating * 20.0; // 5分制转换为100分制
    }

    /**
     * 执行分配算法
     */
    private List<AccommodationRoomDO> executeAllocationAlgorithm(List<RoomScore> roomScores, RoomRequirement requirement) {
        List<AccommodationRoomDO> allocatedRooms = new ArrayList<>();
        int neededRooms = requirement.getRoomCount();

        // 贪心算法：按评分从高到低分配
        for (RoomScore roomScore : roomScores) {
            if (allocatedRooms.size() >= neededRooms) {
                break;
            }

            AccommodationRoomDO room = roomScore.getRoom();

            // 检查房间是否可分配
            if (isRoomAllocatable(room, requirement)) {
                allocatedRooms.add(room);
            }
        }

        return allocatedRooms;
    }

    /**
     * 尝试替代分配方案
     */
    private RoomAllocationResult tryAlternativeAllocation(RoomRequirement requirement, List<AccommodationRoomDO> availableRooms) {
        RoomAllocationResult result = new RoomAllocationResult();

        // 方案1：降低房间类型要求
        if (requirement.getPreferredRoomType() > 1) {
            RoomRequirement lowerRequirement = requirement.copy();
            lowerRequirement.setPreferredRoomType(requirement.getPreferredRoomType() - 1);

            List<AccommodationRoomDO> lowerTypeRooms = getAvailableRooms(lowerRequirement);
            if (lowerTypeRooms.size() >= requirement.getRoomCount()) {
                result.setSuccess(true);
                result.setAllocatedRooms(lowerTypeRooms.subList(0, requirement.getRoomCount()));
                result.setMessage("已为您分配较低级别的房间");
                result.setAlternative(true);
                return result;
            }
        }

        // 方案2：调整入住时间
        LocalDate originalCheckIn = requirement.getCheckInDate();
        for (int i = 1; i <= 3; i++) {
            RoomRequirement adjustedRequirement = requirement.copy();
            adjustedRequirement.setCheckInDate(originalCheckIn.plusDays(i));
            adjustedRequirement.setCheckOutDate(requirement.getCheckOutDate().plusDays(i));

            List<AccommodationRoomDO> adjustedRooms = getAvailableRooms(adjustedRequirement);
            if (adjustedRooms.size() >= requirement.getRoomCount()) {
                result.setSuccess(true);
                result.setAllocatedRooms(adjustedRooms.subList(0, requirement.getRoomCount()));
                result.setMessage(String.format("建议将入住时间调整至%s", adjustedRequirement.getCheckInDate()));
                result.setAlternative(true);
                return result;
            }
        }

        // 方案3：外部酒店推荐
        List<ExternalHotel> nearbyHotels = externalHotelService.findNearbyHotels(requirement);
        if (!nearbyHotels.isEmpty()) {
            result.setSuccess(false);
            result.setMessage("园区内房间不足，推荐附近酒店");
            result.setExternalHotels(nearbyHotels);
            return result;
        }

        result.setSuccess(false);
        result.setMessage("暂无可用房间，建议调整申请时间或联系管理员");
        return result;
    }
}
```

#### 2. 费用计算算法

```java
/**
 * 接待费用计算服务
 */
@Service
@Slf4j
public class ReceptionCostCalculationService {

    /**
     * 计算接待总费用
     */
    public CostCalculationResult calculateTotalCost(ReceptionApplicationDO application) {
        CostCalculationResult result = new CostCalculationResult();

        BigDecimal totalCost = BigDecimal.ZERO;
        List<CostItem> costItems = new ArrayList<>();

        // 1. 计算住宿费用
        if (application.getNeedAccommodation() == 1) {
            CostItem accommodationCost = calculateAccommodationCost(application);
            costItems.add(accommodationCost);
            totalCost = totalCost.add(accommodationCost.getAmount());
        }

        // 2. 计算餐费
        if (application.getNeedDining() == 1) {
            CostItem diningCost = calculateDiningCost(application);
            costItems.add(diningCost);
            totalCost = totalCost.add(diningCost.getAmount());
        }

        // 3. 计算服务费
        CostItem serviceCost = calculateServiceCost(application, totalCost);
        if (serviceCost.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            costItems.add(serviceCost);
            totalCost = totalCost.add(serviceCost.getAmount());
        }

        // 4. 应用折扣
        DiscountResult discount = calculateDiscount(application, totalCost);
        if (discount.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
            CostItem discountItem = new CostItem();
            discountItem.setType("折扣");
            discountItem.setAmount(discount.getDiscountAmount().negate());
            discountItem.setDescription(discount.getDescription());
            costItems.add(discountItem);
            totalCost = totalCost.subtract(discount.getDiscountAmount());
        }

        result.setTotalCost(totalCost);
        result.setCostItems(costItems);
        result.setDiscount(discount);

        return result;
    }

    /**
     * 计算住宿费用
     */
    private CostItem calculateAccommodationCost(ReceptionApplicationDO application) {
        CostItem costItem = new CostItem();
        costItem.setType("住宿费");

        // 获取房间单价
        BigDecimal dailyRate = getRoomDailyRate(application.getRoomType(), application.getReceptionLevel());

        // 计算住宿天数
        int accommodationDays = application.getAccommodationDays();

        // 计算房间数量
        int roomCount = application.getRoomCount();

        // 基础费用 = 日房费 × 天数 × 房间数
        BigDecimal baseCost = dailyRate.multiply(BigDecimal.valueOf(accommodationDays))
                                      .multiply(BigDecimal.valueOf(roomCount));

        // 应用住宿费率调整
        BigDecimal adjustmentRate = getAccommodationAdjustmentRate(application);
        BigDecimal finalCost = baseCost.multiply(adjustmentRate);

        costItem.setAmount(finalCost);
        costItem.setDescription(String.format("%s房间 × %d间 × %d天 × %.0f元/间/天",
            getRoomTypeName(application.getRoomType()), roomCount, accommodationDays, dailyRate));

        return costItem;
    }

    /**
     * 计算餐费
     */
    private CostItem calculateDiningCost(ReceptionApplicationDO application) {
        CostItem costItem = new CostItem();
        costItem.setType("餐费");

        BigDecimal totalDiningCost = BigDecimal.ZERO;
        StringBuilder description = new StringBuilder();

        // 获取餐费标准
        MealPriceStandard priceStandard = getMealPriceStandard(application.getMealStandard());

        // 早餐费用
        if (application.getBreakfastCount() > 0) {
            BigDecimal breakfastCost = priceStandard.getBreakfastPrice()
                .multiply(BigDecimal.valueOf(application.getBreakfastCount()));
            totalDiningCost = totalDiningCost.add(breakfastCost);
            description.append(String.format("早餐 %d人次 × %.0f元 = %.0f元; ",
                application.getBreakfastCount(), priceStandard.getBreakfastPrice(), breakfastCost));
        }

        // 午餐费用
        if (application.getLunchCount() > 0) {
            BigDecimal lunchCost = priceStandard.getLunchPrice()
                .multiply(BigDecimal.valueOf(application.getLunchCount()));
            totalDiningCost = totalDiningCost.add(lunchCost);
            description.append(String.format("午餐 %d人次 × %.0f元 = %.0f元; ",
                application.getLunchCount(), priceStandard.getLunchPrice(), lunchCost));
        }

        // 晚餐费用
        if (application.getDinnerCount() > 0) {
            BigDecimal dinnerCost = priceStandard.getDinnerPrice()
                .multiply(BigDecimal.valueOf(application.getDinnerCount()));
            totalDiningCost = totalDiningCost.add(dinnerCost);
            description.append(String.format("晚餐 %d人次 × %.0f元 = %.0f元; ",
                application.getDinnerCount(), priceStandard.getDinnerPrice(), dinnerCost));
        }

        costItem.setAmount(totalDiningCost);
        costItem.setDescription(description.toString());

        return costItem;
    }

    /**
     * 计算服务费
     */
    private CostItem calculateServiceCost(ReceptionApplicationDO application, BigDecimal baseCost) {
        CostItem costItem = new CostItem();
        costItem.setType("服务费");

        // 根据接待级别确定服务费率
        BigDecimal serviceRate = getServiceRate(application.getReceptionLevel());
        BigDecimal serviceCost = baseCost.multiply(serviceRate);

        costItem.setAmount(serviceCost);
        costItem.setDescription(String.format("服务费 (%.1f%%)", serviceRate.multiply(BigDecimal.valueOf(100))));

        return costItem;
    }

    /**
     * 计算折扣
     */
    private DiscountResult calculateDiscount(ReceptionApplicationDO application, BigDecimal totalCost) {
        DiscountResult result = new DiscountResult();
        result.setDiscountAmount(BigDecimal.ZERO);

        // 1. VIP客户折扣
        if (application.getReceptionLevel() == 3) { // VIP
            BigDecimal vipDiscount = totalCost.multiply(BigDecimal.valueOf(0.1)); // 9折
            result.setDiscountAmount(vipDiscount);
            result.setDescription("VIP客户9折优惠");
            return result;
        }

        // 2. 长期住宿折扣
        if (application.getAccommodationDays() >= 7) {
            BigDecimal longStayDiscount = totalCost.multiply(BigDecimal.valueOf(0.05)); // 95折
            result.setDiscountAmount(longStayDiscount);
            result.setDescription("长期住宿95折优惠");
            return result;
        }

        // 3. 团体折扣
        if (application.getVisitorCount() >= 5) {
            BigDecimal groupDiscount = totalCost.multiply(BigDecimal.valueOf(0.03)); // 97折
            result.setDiscountAmount(groupDiscount);
            result.setDescription("团体客户97折优惠");
            return result;
        }

        return result;
    }
}
```

## 集成方案深度细化

### 外部系统集成

#### 1. 住宿管理系统集成

**A. 房间资源同步服务**

```java
/**
 * 房间资源同步服务
 */
@Service
@Slf4j
public class AccommodationIntegrationService {

    @Resource
    private AccommodationSystemApiClient accommodationApiClient;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 同步房间资源信息
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟同步一次
    public void syncRoomResources() {
        try {
            log.info("开始同步房间资源信息");

            // 1. 获取住宿系统房间列表
            List<ExternalRoomInfo> externalRooms = accommodationApiClient.getAllRooms();

            // 2. 转换为内部数据格式
            List<AccommodationRoomDO> internalRooms = convertToInternalRooms(externalRooms);

            // 3. 更新本地数据库
            updateLocalRoomData(internalRooms);

            // 4. 更新缓存
            updateRoomCache(internalRooms);

            log.info("房间资源同步完成，共同步{}间房间", internalRooms.size());

        } catch (Exception e) {
            log.error("房间资源同步失败", e);
            // 发送告警通知
            sendSyncFailureAlert("房间资源同步", e.getMessage());
        }
    }

    /**
     * 实时查询房间可用性
     */
    public RoomAvailabilityResult checkRoomAvailability(RoomAvailabilityQuery query) {
        try {
            // 1. 先查询缓存
            String cacheKey = buildAvailabilityCacheKey(query);
            RoomAvailabilityResult cachedResult = (RoomAvailabilityResult) redisTemplate.opsForValue().get(cacheKey);

            if (cachedResult != null) {
                return cachedResult;
            }

            // 2. 调用外部系统API
            ExternalAvailabilityQuery externalQuery = convertToExternalQuery(query);
            ExternalAvailabilityResult externalResult = accommodationApiClient.checkAvailability(externalQuery);

            // 3. 转换结果格式
            RoomAvailabilityResult result = convertToInternalResult(externalResult);

            // 4. 缓存结果（缓存5分钟）
            redisTemplate.opsForValue().set(cacheKey, result, Duration.ofMinutes(5));

            return result;

        } catch (Exception e) {
            log.error("查询房间可用性失败", e);
            // 返回降级结果
            return getFallbackAvailabilityResult(query);
        }
    }

    /**
     * 预订房间
     */
    @Transactional(rollbackFor = Exception.class)
    public RoomReservationResult reserveRoom(RoomReservationRequest request) {
        try {
            // 1. 转换预订请求
            ExternalReservationRequest externalRequest = convertToExternalReservation(request);

            // 2. 调用外部系统预订接口
            ExternalReservationResult externalResult = accommodationApiClient.reserveRoom(externalRequest);

            if (externalResult.isSuccess()) {
                // 3. 创建本地住宿安排记录
                ReceptionAccommodationDO accommodation = createAccommodationRecord(request, externalResult);

                // 4. 更新房间状态缓存
                updateRoomStatusCache(request.getRoomId(), "RESERVED");

                // 5. 发送确认通知
                sendReservationConfirmation(accommodation);

                return RoomReservationResult.success(accommodation.getId(), externalResult.getReservationNo());
            } else {
                return RoomReservationResult.failure(externalResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("房间预订失败", e);
            return RoomReservationResult.failure("系统异常，预订失败");
        }
    }

    /**
     * 取消房间预订
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelReservation(Long accommodationId, String reason) {
        try {
            // 1. 获取住宿安排信息
            ReceptionAccommodationDO accommodation = accommodationService.getById(accommodationId);
            if (accommodation == null) {
                throw new BusinessException("住宿安排不存在");
            }

            // 2. 调用外部系统取消接口
            boolean cancelSuccess = accommodationApiClient.cancelReservation(
                accommodation.getExternalReservationNo(), reason);

            if (cancelSuccess) {
                // 3. 更新本地状态
                accommodation.setStatus(AccommodationStatusEnum.CANCELLED.getStatus());
                accommodationService.updateById(accommodation);

                // 4. 清除缓存
                clearRoomStatusCache(accommodation.getRoomId());

                // 5. 发送取消通知
                sendCancellationNotification(accommodation, reason);

                return true;
            } else {
                log.error("外部系统取消预订失败，住宿安排ID：{}", accommodationId);
                return false;
            }

        } catch (Exception e) {
            log.error("取消房间预订失败，住宿安排ID：{}", accommodationId, e);
            return false;
        }
    }

    /**
     * 处理外部系统状态变更回调
     */
    @PostMapping("/webhook/accommodation/status-change")
    public void handleStatusChangeWebhook(@RequestBody AccommodationStatusChangeWebhook webhook) {
        try {
            log.info("收到住宿状态变更回调：{}", JSON.toJSONString(webhook));

            // 1. 验证回调签名
            if (!validateWebhookSignature(webhook)) {
                log.warn("住宿状态变更回调签名验证失败");
                return;
            }

            // 2. 查找对应的住宿安排
            ReceptionAccommodationDO accommodation = accommodationService.getByExternalReservationNo(
                webhook.getReservationNo());

            if (accommodation == null) {
                log.warn("未找到对应的住宿安排，外部预订号：{}", webhook.getReservationNo());
                return;
            }

            // 3. 更新本地状态
            updateAccommodationStatus(accommodation, webhook);

            // 4. 发送状态变更通知
            sendStatusChangeNotification(accommodation, webhook);

        } catch (Exception e) {
            log.error("处理住宿状态变更回调失败", e);
        }
    }
}
```

#### 2. 饭卡系统集成

**A. 饭卡管理服务**

```java
/**
 * 饭卡系统集成服务
 */
@Service
@Slf4j
public class MealCardIntegrationService {

    @Resource
    private MealCardSystemApiClient mealCardApiClient;

    @Resource
    private ReceptionDiningService diningService;

    /**
     * 申领饭卡
     */
    @Transactional(rollbackFor = Exception.class)
    public MealCardIssueResult issueMealCard(MealCardIssueRequest request) {
        try {
            // 1. 验证申请信息
            validateMealCardRequest(request);

            // 2. 调用饭卡系统申领接口
            ExternalMealCardRequest externalRequest = convertToExternalMealCardRequest(request);
            ExternalMealCardResult externalResult = mealCardApiClient.issueMealCard(externalRequest);

            if (externalResult.isSuccess()) {
                // 3. 更新就餐安排记录
                updateDiningMealCardInfo(request.getDiningId(), externalResult.getCardNo());

                // 4. 记录饭卡申领日志
                recordMealCardOperation(request.getDiningId(), "ISSUE", externalResult.getCardNo(), "饭卡申领成功");

                return MealCardIssueResult.success(externalResult.getCardNo());
            } else {
                return MealCardIssueResult.failure(externalResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("饭卡申领失败", e);
            return MealCardIssueResult.failure("系统异常，申领失败");
        }
    }

    /**
     * 归还饭卡
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean returnMealCard(String cardNo, Long diningId) {
        try {
            // 1. 查询饭卡余额
            BigDecimal balance = mealCardApiClient.getCardBalance(cardNo);

            // 2. 处理余额退还
            if (balance.compareTo(BigDecimal.ZERO) > 0) {
                boolean refundSuccess = mealCardApiClient.refundBalance(cardNo, balance);
                if (!refundSuccess) {
                    log.warn("饭卡余额退还失败，卡号：{}，余额：{}", cardNo, balance);
                }
            }

            // 3. 调用饭卡系统归还接口
            boolean returnSuccess = mealCardApiClient.returnMealCard(cardNo);

            if (returnSuccess) {
                // 4. 更新就餐安排状态
                updateDiningMealCardStatus(diningId, false);

                // 5. 记录饭卡归还日志
                recordMealCardOperation(diningId, "RETURN", cardNo, "饭卡归还成功");

                return true;
            } else {
                log.error("饭卡归还失败，卡号：{}", cardNo);
                return false;
            }

        } catch (Exception e) {
            log.error("饭卡归还失败，卡号：{}", cardNo, e);
            return false;
        }
    }

    /**
     * 同步就餐消费记录
     */
    @Scheduled(fixedDelay = 60000) // 每分钟同步一次
    public void syncDiningRecords() {
        try {
            // 1. 获取需要同步的就餐安排
            List<ReceptionDiningDO> activeDinings = diningService.getActiveDinings();

            for (ReceptionDiningDO dining : activeDinings) {
                if (StringUtils.isNotBlank(dining.getMealCardNo())) {
                    syncDiningRecordsForCard(dining);
                }
            }

        } catch (Exception e) {
            log.error("同步就餐消费记录失败", e);
        }
    }

    /**
     * 同步单个饭卡的消费记录
     */
    private void syncDiningRecordsForCard(ReceptionDiningDO dining) {
        try {
            // 1. 获取最后同步时间
            LocalDateTime lastSyncTime = getLastSyncTime(dining.getId());

            // 2. 查询饭卡系统消费记录
            List<ExternalDiningRecord> externalRecords = mealCardApiClient.getDiningRecords(
                dining.getMealCardNo(), lastSyncTime);

            if (externalRecords.isEmpty()) {
                return;
            }

            // 3. 转换并保存本地记录
            List<ReceptionDiningRecordDO> localRecords = convertToLocalDiningRecords(externalRecords, dining);
            diningRecordService.saveBatch(localRecords);

            // 4. 更新同步时间
            updateLastSyncTime(dining.getId(), LocalDateTime.now());

            // 5. 更新消费统计
            updateDiningConsumptionStats(dining.getId());

            log.info("同步饭卡消费记录完成，卡号：{}，记录数：{}", dining.getMealCardNo(), localRecords.size());

        } catch (Exception e) {
            log.error("同步饭卡消费记录失败，就餐安排ID：{}", dining.getId(), e);
        }
    }
}
```

#### 3. 人脸识别系统集成

**A. 人脸识别服务**

```java
/**
 * 人脸识别系统集成服务
 */
@Service
@Slf4j
public class FaceRecognitionIntegrationService {

    @Resource
    private FaceRecognitionApiClient faceApiClient;

    @Resource
    private FileService fileService;

    /**
     * 注册访客人脸信息
     */
    @Transactional(rollbackFor = Exception.class)
    public FaceRegistrationResult registerVisitorFace(FaceRegistrationRequest request) {
        try {
            // 1. 验证人脸照片
            FaceValidationResult validation = validateFacePhoto(request.getPhotoUrl());
            if (!validation.isValid()) {
                return FaceRegistrationResult.failure(validation.getErrorMessage());
            }

            // 2. 提取人脸特征
            FaceFeatureExtractionResult featureResult = faceApiClient.extractFaceFeature(request.getPhotoUrl());
            if (!featureResult.isSuccess()) {
                return FaceRegistrationResult.failure("人脸特征提取失败：" + featureResult.getErrorMessage());
            }

            // 3. 检查人脸是否已存在
            FaceSearchResult searchResult = faceApiClient.searchFace(featureResult.getFeatureData());
            if (searchResult.isFound() && searchResult.getSimilarity() > 0.8) {
                return FaceRegistrationResult.failure("该人脸已存在于系统中");
            }

            // 4. 注册人脸到识别系统
            FaceRegistrationRequest externalRequest = FaceRegistrationRequest.builder()
                .personId(request.getVisitorId().toString())
                .personName(request.getVisitorName())
                .featureData(featureResult.getFeatureData())
                .photoUrl(request.getPhotoUrl())
                .groupId("VISITOR_GROUP")
                .build();

            ExternalFaceRegistrationResult externalResult = faceApiClient.registerFace(externalRequest);

            if (externalResult.isSuccess()) {
                // 5. 更新就餐安排的人脸信息
                updateDiningFaceInfo(request.getDiningId(), request.getPhotoUrl(), featureResult.getFeatureData());

                // 6. 记录人脸注册日志
                recordFaceOperation(request.getDiningId(), "REGISTER", request.getVisitorName(), "人脸注册成功");

                return FaceRegistrationResult.success(externalResult.getFaceId());
            } else {
                return FaceRegistrationResult.failure(externalResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("注册访客人脸信息失败", e);
            return FaceRegistrationResult.failure("系统异常，注册失败");
        }
    }

    /**
     * 人脸识别就餐
     */
    public FaceRecognitionDiningResult recognizeDining(FaceRecognitionDiningRequest request) {
        try {
            // 1. 人脸识别
            FaceRecognitionResult recognitionResult = faceApiClient.recognizeFace(
                request.getFaceImageUrl(), "VISITOR_GROUP");

            if (!recognitionResult.isSuccess()) {
                return FaceRecognitionDiningResult.failure("人脸识别失败");
            }

            if (recognitionResult.getSimilarity() < 0.7) {
                return FaceRecognitionDiningResult.failure("人脸匹配度过低，无法确认身份");
            }

            // 2. 查找对应的就餐安排
            String visitorId = recognitionResult.getPersonId();
            ReceptionDiningDO dining = diningService.getActiveByVisitorId(Long.valueOf(visitorId));

            if (dining == null) {
                return FaceRecognitionDiningResult.failure("未找到有效的就餐安排");
            }

            // 3. 验证就餐权限
            DiningPermissionResult permission = validateDiningPermission(dining, request);
            if (!permission.isAllowed()) {
                return FaceRecognitionDiningResult.failure(permission.getReason());
            }

            // 4. 记录就餐消费
            ReceptionDiningRecordDO diningRecord = createDiningRecord(dining, request, recognitionResult);
            diningRecordService.save(diningRecord);

            // 5. 更新消费统计
            updateDiningConsumptionStats(dining.getId());

            return FaceRecognitionDiningResult.success(diningRecord.getId(), recognitionResult.getSimilarity());

        } catch (Exception e) {
            log.error("人脸识别就餐失败", e);
            return FaceRecognitionDiningResult.failure("系统异常，识别失败");
        }
    }

    /**
     * 删除访客人脸信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVisitorFace(Long diningId) {
        try {
            // 1. 获取就餐安排信息
            ReceptionDiningDO dining = diningService.getById(diningId);
            if (dining == null) {
                return false;
            }

            // 2. 从人脸识别系统删除
            boolean deleteSuccess = faceApiClient.deleteFace(dining.getVisitorId().toString(), "VISITOR_GROUP");

            if (deleteSuccess) {
                // 3. 清除本地人脸信息
                dining.setFacePhotoUrl(null);
                dining.setFaceFeatureData(null);
                dining.setFaceRecognitionEnabled(0);
                diningService.updateById(dining);

                // 4. 记录删除日志
                recordFaceOperation(diningId, "DELETE", dining.getVisitorName(), "人脸信息删除成功");

                return true;
            } else {
                log.error("从人脸识别系统删除人脸失败，就餐安排ID：{}", diningId);
                return false;
            }

        } catch (Exception e) {
            log.error("删除访客人脸信息失败，就餐安排ID：{}", diningId, e);
            return false;
        }
    }

    /**
     * 验证人脸照片质量
     */
    private FaceValidationResult validateFacePhoto(String photoUrl) {
        try {
            // 1. 下载照片
            byte[] photoData = fileService.downloadFile(photoUrl);

            // 2. 调用人脸质量检测API
            FaceQualityResult qualityResult = faceApiClient.checkFaceQuality(photoData);

            if (!qualityResult.isSuccess()) {
                return FaceValidationResult.invalid("人脸质量检测失败");
            }

            // 3. 检查人脸质量指标
            if (qualityResult.getFaceCount() == 0) {
                return FaceValidationResult.invalid("照片中未检测到人脸");
            }

            if (qualityResult.getFaceCount() > 1) {
                return FaceValidationResult.invalid("照片中检测到多张人脸，请使用单人照片");
            }

            if (qualityResult.getClarity() < 0.6) {
                return FaceValidationResult.invalid("照片清晰度不足，请使用更清晰的照片");
            }

            if (qualityResult.getBrightness() < 0.3 || qualityResult.getBrightness() > 0.9) {
                return FaceValidationResult.invalid("照片亮度不合适，请使用光线适中的照片");
            }

            return FaceValidationResult.valid();

        } catch (Exception e) {
            log.error("验证人脸照片质量失败", e);
            return FaceValidationResult.invalid("照片验证失败");
        }
    }
}
```

## 用户体验深度细化

### 前端交互流程详细设计

#### A. 接待申请页面交互控制器

```javascript
/**
 * 接待申请页面交互控制器
 */
class ReceptionApplicationController {

    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.formData = {};
        this.validationRules = this.initValidationRules();
        this.autoSaveTimer = null;
        this.costCalculationTimer = null;
    }

    /**
     * 初始化页面
     */
    init() {
        this.initStepIndicator();
        this.initFormValidation();
        this.initAutoSave();
        this.initVisitorSelector();
        this.initResourceSelector();
        this.bindEvents();
    }

    /**
     * 步骤指示器
     */
    initStepIndicator() {
        const steps = [
            { title: '关联访客', icon: 'user', description: '选择已审批的访客申请' },
            { title: '服务配置', icon: 'setting', description: '配置住宿和就餐服务' },
            { title: '费用确认', icon: 'money', description: '确认服务费用' },
            { title: '提交申请', icon: 'check', description: '提交审批申请' }
        ];

        this.renderStepIndicator(steps);
    }

    /**
     * 访客申请选择器
     */
    initVisitorSelector() {
        $('#visitorApplicationSelector').on('click', () => {
            this.showVisitorApplicationModal();
        });
    }

    /**
     * 显示访客申请选择模态框
     */
    showVisitorApplicationModal() {
        // 获取已审批的访客申请列表
        this.getApprovedVisitorApplications()
            .then(applications => {
                this.renderVisitorApplicationModal(applications);
            })
            .catch(error => {
                this.showError('获取访客申请列表失败：' + error.message);
            });
    }

    /**
     * 渲染访客申请选择模态框
     */
    renderVisitorApplicationModal(applications) {
        const modalHtml = `
            <div class="visitor-application-modal">
                <div class="modal-header">
                    <h3>选择访客申请</h3>
                    <button class="close-btn" onclick="this.closeVisitorModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="application-filter">
                        <input type="text" id="applicationSearch" placeholder="搜索申请单号或访客姓名">
                        <select id="visitorTypeFilter">
                            <option value="">所有类型</option>
                            <option value="1">普通访客</option>
                            <option value="2">政府访客</option>
                            <option value="3">施工承包商</option>
                        </select>
                        <input type="date" id="visitDateFilter" placeholder="访问日期">
                    </div>
                    <div class="application-list">
                        ${this.renderApplicationList(applications)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closeVisitorModal()">取消</button>
                    <button class="btn btn-primary" onclick="this.confirmVisitorSelection()">确认选择</button>
                </div>
            </div>
        `;

        $('body').append(modalHtml);
        $('.visitor-application-modal').show();

        // 绑定搜索和筛选事件
        this.bindApplicationFilterEvents();
    }

    /**
     * 渲染访客申请列表
     */
    renderApplicationList(applications) {
        return applications.map(app => `
            <div class="application-item" data-application-id="${app.id}">
                <div class="application-info">
                    <div class="application-header">
                        <span class="application-no">${app.applicationNo}</span>
                        <span class="visitor-type ${this.getVisitorTypeClass(app.visitorType)}">${this.getVisitorTypeName(app.visitorType)}</span>
                    </div>
                    <div class="visitor-details">
                        <div class="visitor-name">${app.visitorName}</div>
                        <div class="visitor-company">${app.companyName}</div>
                        <div class="visit-info">
                            <span class="visit-time">${app.visitStartTime} - ${app.visitEndTime}</span>
                            <span class="contact-person">联系人：${app.contactPerson}</span>
                        </div>
                        <div class="visit-reason">${app.visitReason}</div>
                    </div>
                </div>
                <div class="application-actions">
                    <button class="btn btn-sm btn-outline-primary select-application"
                            data-application-id="${app.id}">选择</button>
                </div>
            </div>
        `).join('');
    }

    /**
     * 服务配置表单验证
     */
    initFormValidation() {
        // 住宿服务配置验证
        $('#needAccommodation').on('change', (e) => {
            const needAccommodation = e.target.checked;
            this.toggleAccommodationFields(needAccommodation);

            if (needAccommodation) {
                this.validateAccommodationConfig();
            }
        });

        // 就餐服务配置验证
        $('#needDining').on('change', (e) => {
            const needDining = e.target.checked;
            this.toggleDiningFields(needDining);

            if (needDining) {
                this.validateDiningConfig();
            }
        });

        // 入住时间验证
        $('#checkInDate, #checkOutDate').on('change', () => {
            this.validateAccommodationDates();
            this.checkRoomAvailability();
            this.calculateEstimatedCost();
        });

        // 就餐时间验证
        $('#diningStartDate, #diningEndDate').on('change', () => {
            this.validateDiningDates();
            this.calculateEstimatedCost();
        });

        // 餐次人数验证
        $('#breakfastCount, #lunchCount, #dinnerCount').on('input', () => {
            this.validateMealCounts();
            this.calculateEstimatedCost();
        });
    }

    /**
     * 住宿配置验证
     */
    validateAccommodationConfig() {
        const checkInDate = new Date($('#checkInDate').val());
        const checkOutDate = new Date($('#checkOutDate').val());
        const roomType = parseInt($('#roomType').val());
        const roomCount = parseInt($('#roomCount').val());

        let isValid = true;

        // 日期验证
        if (!this.validateAccommodationDates()) {
            isValid = false;
        }

        // 房间类型验证
        if (!roomType) {
            this.showFieldValidation('roomType', {
                valid: false,
                message: '请选择房间类型'
            });
            isValid = false;
        }

        // 房间数量验证
        if (!roomCount || roomCount < 1) {
            this.showFieldValidation('roomCount', {
                valid: false,
                message: '房间数量不能少于1间'
            });
            isValid = false;
        }

        if (roomCount > 10) {
            this.showFieldValidation('roomCount', {
                valid: false,
                message: '房间数量不能超过10间'
            });
            isValid = false;
        }

        return isValid;
    }

    /**
     * 住宿日期验证
     */
    validateAccommodationDates() {
        const checkInDate = new Date($('#checkInDate').val());
        const checkOutDate = new Date($('#checkOutDate').val());
        const now = new Date();

        // 入住日期不能早于当前日期
        if (checkInDate < now) {
            this.showFieldValidation('checkInDate', {
                valid: false,
                message: '入住日期不能早于当前日期'
            });
            return false;
        }

        // 退宿日期必须晚于入住日期
        if (checkOutDate <= checkInDate) {
            this.showFieldValidation('checkOutDate', {
                valid: false,
                message: '退宿日期必须晚于入住日期'
            });
            return false;
        }

        // 住宿时长不能超过30天
        const duration = (checkOutDate - checkInDate) / (1000 * 60 * 60 * 24);
        if (duration > 30) {
            this.showFieldValidation('checkOutDate', {
                valid: false,
                message: '住宿时长不能超过30天'
            });
            return false;
        }

        // 清除错误提示
        this.clearFieldValidation('checkInDate');
        this.clearFieldValidation('checkOutDate');

        return true;
    }

    /**
     * 检查房间可用性
     */
    checkRoomAvailability() {
        const checkInDate = $('#checkInDate').val();
        const checkOutDate = $('#checkOutDate').val();
        const roomType = parseInt($('#roomType').val());
        const roomCount = parseInt($('#roomCount').val());

        if (!checkInDate || !checkOutDate || !roomType || !roomCount) {
            return;
        }

        // 显示检查状态
        this.showRoomAvailabilityStatus('checking');

        // 调用房间可用性检查API
        this.apiCall('/api/reception/room/availability', {
            checkInDate: checkInDate,
            checkOutDate: checkOutDate,
            roomType: roomType,
            roomCount: roomCount
        }).then(result => {
            if (result.available) {
                this.showRoomAvailabilityStatus('available', {
                    availableCount: result.availableCount,
                    totalCount: result.totalCount
                });
            } else {
                this.showRoomAvailabilityStatus('unavailable', {
                    availableCount: result.availableCount,
                    neededCount: roomCount
                });

                // 提供替代方案
                if (result.alternatives && result.alternatives.length > 0) {
                    this.showAlternativeRoomOptions(result.alternatives);
                }
            }
        }).catch(error => {
            this.showRoomAvailabilityStatus('error');
            console.error('房间可用性检查失败', error);
        });
    }

    /**
     * 显示房间可用性状态
     */
    showRoomAvailabilityStatus(status, data = {}) {
        const statusContainer = $('#roomAvailabilityStatus');

        switch (status) {
            case 'checking':
                statusContainer.html(`
                    <div class="availability-status checking">
                        <i class="icon-loading"></i>
                        <span>正在检查房间可用性...</span>
                    </div>
                `);
                break;

            case 'available':
                statusContainer.html(`
                    <div class="availability-status available">
                        <i class="icon-check-circle"></i>
                        <span>房间充足，可用${data.availableCount}间（共${data.totalCount}间）</span>
                    </div>
                `);
                break;

            case 'unavailable':
                statusContainer.html(`
                    <div class="availability-status unavailable">
                        <i class="icon-warning-circle"></i>
                        <span>房间不足，可用${data.availableCount}间，需要${data.neededCount}间</span>
                    </div>
                `);
                break;

            case 'error':
                statusContainer.html(`
                    <div class="availability-status error">
                        <i class="icon-error-circle"></i>
                        <span>检查房间可用性失败，请重试</span>
                    </div>
                `);
                break;
        }
    }

    /**
     * 实时费用计算
     */
    calculateEstimatedCost() {
        // 防抖处理
        clearTimeout(this.costCalculationTimer);
        this.costCalculationTimer = setTimeout(() => {
            this.performCostCalculation();
        }, 500);
    }

    /**
     * 执行费用计算
     */
    performCostCalculation() {
        const formData = this.collectFormData();

        if (!this.isFormDataValidForCostCalculation(formData)) {
            return;
        }

        // 显示计算状态
        this.showCostCalculationStatus('calculating');

        // 调用费用计算API
        this.apiCall('/api/reception/cost/calculate', formData)
            .then(result => {
                this.showCostBreakdown(result);
                this.updateTotalCost(result.totalCost);
            })
            .catch(error => {
                this.showCostCalculationStatus('error');
                console.error('费用计算失败', error);
            });
    }

    /**
     * 显示费用明细
     */
    showCostBreakdown(costResult) {
        const breakdownHtml = `
            <div class="cost-breakdown">
                <div class="cost-header">
                    <h4>费用明细</h4>
                </div>
                <div class="cost-items">
                    ${costResult.costItems.map(item => `
                        <div class="cost-item">
                            <div class="cost-item-header">
                                <span class="cost-type">${item.type}</span>
                                <span class="cost-amount">¥${item.amount.toFixed(2)}</span>
                            </div>
                            <div class="cost-description">${item.description}</div>
                        </div>
                    `).join('')}
                </div>
                ${costResult.discount && costResult.discount.discountAmount > 0 ? `
                    <div class="cost-discount">
                        <div class="cost-item">
                            <div class="cost-item-header">
                                <span class="cost-type discount">优惠折扣</span>
                                <span class="cost-amount discount">-¥${costResult.discount.discountAmount.toFixed(2)}</span>
                            </div>
                            <div class="cost-description">${costResult.discount.description}</div>
                        </div>
                    </div>
                ` : ''}
                <div class="cost-total">
                    <div class="cost-item total">
                        <div class="cost-item-header">
                            <span class="cost-type">总计</span>
                            <span class="cost-amount">¥${costResult.totalCost.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#costBreakdown').html(breakdownHtml);
    }

    /**
     * 提交申请
     */
    async submitApplication() {
        // 最终验证
        if (!this.validateAllSteps()) {
            return;
        }

        this.showLoading('正在提交申请...');

        try {
            const formData = this.collectFormData();
            const response = await this.apiCall('/api/reception/application/create', formData);

            if (response.success) {
                this.showSuccess('申请提交成功！', () => {
                    window.location.href = '/reception/application/success?id=' + response.data.id;
                });
            } else {
                this.showError(response.message);
            }
        } catch (error) {
            this.showError('提交失败，请重试');
        } finally {
            this.hideLoading();
        }
    }
}
```

#### B. 移动端适配方案

```css
/* 接待管理移动端响应式设计 */
@media (max-width: 768px) {
    .reception-application-form {
        padding: 10px;
        margin: 0;
    }

    .step-indicator {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 20px;
    }

    .step-item {
        font-size: 12px;
        padding: 8px 12px;
        border-radius: 4px;
    }

    .step-item .step-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }

    .visitor-application-modal {
        width: 95%;
        height: 90%;
        margin: 5% auto;
    }

    .application-item {
        flex-direction: column;
        padding: 15px;
        margin-bottom: 10px;
    }

    .application-info {
        margin-bottom: 10px;
    }

    .application-actions {
        width: 100%;
        justify-content: center;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
        border-radius: 6px;
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .form-col {
        width: 100%;
        margin-bottom: 15px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        padding: 12px;
        font-size: 16px;
        min-height: 44px;
    }

    .cost-breakdown {
        font-size: 14px;
        padding: 15px;
    }

    .cost-item {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }

    .cost-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .room-availability-status {
        padding: 10px;
        font-size: 14px;
    }

    .service-config-section {
        margin-bottom: 20px;
        padding: 15px;
        border-radius: 8px;
        background: #f8f9fa;
    }

    .service-toggle {
        margin-bottom: 15px;
    }

    .service-fields {
        display: none;
    }

    .service-fields.active {
        display: block;
    }
}

/* 触摸优化 */
.touch-device .form-control {
    min-height: 44px; /* iOS推荐最小触摸区域 */
}

.touch-device .btn {
    min-height: 44px;
    padding: 12px 20px;
}

.touch-device .application-item {
    min-height: 80px;
    padding: 15px;
}

.touch-device .step-item {
    min-height: 44px;
    padding: 12px;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .reception-application-form {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .form-control {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }

    .application-item {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .visitor-application-modal {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .cost-breakdown {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .service-config-section {
        background-color: #2d2d2d;
    }
}

/* 无障碍访问支持 */
.form-control:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.application-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .form-control {
        border: 2px solid #000000;
    }

    .btn {
        border: 2px solid #000000;
    }

    .application-item {
        border: 2px solid #000000;
    }
}
```

## 运维监控深度细化

### 系统监控指标设计

#### A. 业务监控指标

```java
/**
 * 接待管理业务监控服务
 */
@Service
@Slf4j
public class ReceptionMonitoringService {

    @Resource
    private MeterRegistry meterRegistry;

    // 业务指标计数器
    private final Counter receptionApplicationCounter;
    private final Counter receptionApprovalCounter;
    private final Counter accommodationCounter;
    private final Counter diningCounter;

    // 业务指标计时器
    private final Timer approvalProcessTimer;
    private final Timer resourceAllocationTimer;
    private final Timer costCalculationTimer;

    // 业务指标仪表盘
    private final Gauge pendingApplicationsGauge;
    private final Gauge occupancyRateGauge;
    private final Gauge averageCostGauge;

    public ReceptionMonitoringService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化计数器
        this.receptionApplicationCounter = Counter.builder("reception.application.submitted")
            .description("接待申请提交总数")
            .register(meterRegistry);

        this.receptionApprovalCounter = Counter.builder("reception.application.approved")
            .description("接待申请审批通过总数")
            .register(meterRegistry);

        this.accommodationCounter = Counter.builder("reception.accommodation.created")
            .description("住宿安排创建总数")
            .register(meterRegistry);

        this.diningCounter = Counter.builder("reception.dining.created")
            .description("就餐安排创建总数")
            .register(meterRegistry);

        // 初始化计时器
        this.approvalProcessTimer = Timer.builder("reception.approval.process.time")
            .description("接待审批流程处理时间")
            .register(meterRegistry);

        this.resourceAllocationTimer = Timer.builder("reception.resource.allocation.time")
            .description("资源分配处理时间")
            .register(meterRegistry);

        this.costCalculationTimer = Timer.builder("reception.cost.calculation.time")
            .description("费用计算处理时间")
            .register(meterRegistry);

        // 初始化仪表盘
        this.pendingApplicationsGauge = Gauge.builder("reception.application.pending.count")
            .description("待处理接待申请数量")
            .register(meterRegistry, this, ReceptionMonitoringService::getPendingApplicationCount);

        this.occupancyRateGauge = Gauge.builder("reception.accommodation.occupancy.rate")
            .description("住宿入住率")
            .register(meterRegistry, this, ReceptionMonitoringService::getAccommodationOccupancyRate);

        this.averageCostGauge = Gauge.builder("reception.cost.average")
            .description("平均接待费用")
            .register(meterRegistry, this, ReceptionMonitoringService::getAverageReceptionCost);
    }

    /**
     * 记录接待申请提交
     */
    public void recordReceptionApplication(String receptionType, String receptionLevel, String department) {
        receptionApplicationCounter.increment(
            Tags.of(
                "reception_type", receptionType,
                "reception_level", receptionLevel,
                "department", department
            )
        );
    }

    /**
     * 记录资源分配
     */
    public void recordResourceAllocation(long allocationTimeMs, boolean success, String resourceType) {
        resourceAllocationTimer.record(allocationTimeMs, TimeUnit.MILLISECONDS);

        Counter.builder("reception.resource.allocation.result")
            .description("资源分配结果")
            .tag("success", String.valueOf(success))
            .tag("resource_type", resourceType)
            .register(meterRegistry)
            .increment();
    }

    /**
     * 记录费用计算
     */
    public void recordCostCalculation(long calculationTimeMs, BigDecimal totalCost, String receptionType) {
        costCalculationTimer.record(calculationTimeMs, TimeUnit.MILLISECONDS);

        Gauge.builder("reception.cost.amount")
            .description("接待费用金额")
            .tag("reception_type", receptionType)
            .register(meterRegistry, this, (self) -> totalCost.doubleValue());
    }

    /**
     * 自定义业务告警
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkBusinessAlerts() {
        // 检查待处理申请积压
        long pendingCount = getPendingApplicationCount();
        if (pendingCount > 50) {
            sendAlert("RECEPTION_APPLICATIONS_HIGH",
                "待处理接待申请数量过多：" + pendingCount,
                AlertLevel.WARNING);
        }

        // 检查住宿入住率
        double occupancyRate = getAccommodationOccupancyRate();
        if (occupancyRate > 0.9) {
            sendAlert("ACCOMMODATION_OCCUPANCY_HIGH",
                "住宿入住率过高：" + String.format("%.2f%%", occupancyRate * 100),
                AlertLevel.WARNING);
        }

        // 检查费用异常
        double averageCost = getAverageReceptionCost();
        double costThreshold = getAverageCostThreshold();
        if (averageCost > costThreshold * 1.5) {
            sendAlert("RECEPTION_COST_HIGH",
                "平均接待费用异常：" + String.format("%.2f元", averageCost),
                AlertLevel.CRITICAL);
        }

        // 检查系统集成状态
        checkIntegrationHealth();
    }

    /**
     * 检查系统集成健康状态
     */
    private void checkIntegrationHealth() {
        // 检查住宿系统集成
        boolean accommodationSystemHealth = checkAccommodationSystemHealth();
        if (!accommodationSystemHealth) {
            sendAlert("ACCOMMODATION_SYSTEM_DOWN",
                "住宿管理系统连接异常",
                AlertLevel.CRITICAL);
        }

        // 检查饭卡系统集成
        boolean mealCardSystemHealth = checkMealCardSystemHealth();
        if (!mealCardSystemHealth) {
            sendAlert("MEAL_CARD_SYSTEM_DOWN",
                "饭卡系统连接异常",
                AlertLevel.CRITICAL);
        }

        // 检查人脸识别系统集成
        boolean faceRecognitionSystemHealth = checkFaceRecognitionSystemHealth();
        if (!faceRecognitionSystemHealth) {
            sendAlert("FACE_RECOGNITION_SYSTEM_DOWN",
                "人脸识别系统连接异常",
                AlertLevel.WARNING);
        }
    }

    private double getPendingApplicationCount() {
        return receptionApplicationService.countByStatus(ReceptionStatusEnum.PENDING_APPROVAL.getStatus());
    }

    private double getAccommodationOccupancyRate() {
        return accommodationService.calculateCurrentOccupancyRate();
    }

    private double getAverageReceptionCost() {
        return receptionCostService.calculateAverageCostLast30Days().doubleValue();
    }
}
```

#### B. 日志记录规范

**1. 结构化日志设计**

```java
/**
 * 接待管理日志服务
 */
@Service
@Slf4j
public class ReceptionLoggingService {

    private static final String LOG_PREFIX = "[RECEPTION]";

    /**
     * 记录接待业务操作日志
     */
    public void logReceptionOperation(String operation, Long targetId, String targetType,
                                    Long userId, Object requestData, Object responseData, long duration) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("operation", operation);
        logData.put("targetId", targetId);
        logData.put("targetType", targetType);
        logData.put("userId", userId);
        logData.put("duration", duration);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("requestData", requestData);
        logData.put("responseData", responseData);

        log.info("{} 接待业务操作 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录接待安全事件日志
     */
    public void logReceptionSecurityEvent(String eventType, String description, String applicationNo,
                                        String ipAddress, String userAgent, Long userId) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("eventType", eventType);
        logData.put("description", description);
        logData.put("applicationNo", applicationNo);
        logData.put("ipAddress", ipAddress);
        logData.put("userAgent", userAgent);
        logData.put("userId", userId);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("severity", "HIGH");

        log.warn("{} 接待安全事件 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录资源分配日志
     */
    public void logResourceAllocation(Long applicationId, String resourceType, Long resourceId,
                                    String allocationResult, String allocationReason) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("applicationId", applicationId);
        logData.put("resourceType", resourceType);
        logData.put("resourceId", resourceId);
        logData.put("allocationResult", allocationResult);
        logData.put("allocationReason", allocationReason);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("type", "RESOURCE_ALLOCATION");

        log.info("{} 资源分配 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录费用计算日志
     */
    public void logCostCalculation(Long applicationId, String calculationType, BigDecimal totalCost,
                                 String costBreakdown, long calculationTime) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("applicationId", applicationId);
        logData.put("calculationType", calculationType);
        logData.put("totalCost", totalCost);
        logData.put("costBreakdown", costBreakdown);
        logData.put("calculationTime", calculationTime);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("type", "COST_CALCULATION");

        log.info("{} 费用计算 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录系统集成日志
     */
    public void logSystemIntegration(String systemName, String operation, String requestData,
                                   String responseData, boolean success, long responseTime) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("systemName", systemName);
        logData.put("operation", operation);
        logData.put("requestData", requestData);
        logData.put("responseData", responseData);
        logData.put("success", success);
        logData.put("responseTime", responseTime);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("type", "SYSTEM_INTEGRATION");

        if (success) {
            log.info("{} 系统集成 - {}", LOG_PREFIX, JSON.toJSONString(logData));
        } else {
            log.error("{} 系统集成失败 - {}", LOG_PREFIX, JSON.toJSONString(logData));
        }
    }
}
```

**2. 日志配置**

```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- 接待业务日志文件 -->
    <appender name="RECEPTION_BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/reception-business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/reception-business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[RECEPTION] 接待业务操作")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 接待安全日志文件 -->
    <appender name="RECEPTION_SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/reception-security.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/reception-security.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <message/>
                <mdc/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[RECEPTION] 接待安全事件")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 系统集成日志文件 -->
    <appender name="RECEPTION_INTEGRATION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/reception-integration.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/reception-integration.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <message/>
                <mdc/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[RECEPTION] 系统集成")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>
</configuration>
```

#### C. 故障排查和应急处理

**1. 故障诊断工具**

```java
/**
 * 接待管理故障诊断服务
 */
@RestController
@RequestMapping("/api/admin/reception/diagnosis")
@PreAuthorize("hasRole('ADMIN')")
public class ReceptionDiagnosisController {

    @Resource
    private ReceptionApplicationService applicationService;

    @Resource
    private AccommodationService accommodationService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 接待系统健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> receptionHealthCheck() {
        Map<String, Object> health = new HashMap<>();

        // 数据库连接检查
        health.put("database", checkReceptionDatabaseHealth());

        // 缓存检查
        health.put("cache", checkReceptionCacheHealth());

        // 业务数据检查
        health.put("business", checkReceptionBusinessHealth());

        // 外部系统检查
        health.put("external", checkReceptionExternalSystemsHealth());

        return Result.success(health);
    }

    /**
     * 资源分配诊断
     */
    @GetMapping("/resource-allocation")
    public Result<Map<String, Object>> resourceAllocationDiagnosis() {
        Map<String, Object> diagnosis = new HashMap<>();

        // 房间资源分析
        diagnosis.put("roomResources", analyzeRoomResources());

        // 餐厅资源分析
        diagnosis.put("diningResources", analyzeDiningResources());

        // 分配算法性能
        diagnosis.put("allocationPerformance", analyzeAllocationPerformance());

        // 资源冲突检测
        diagnosis.put("conflicts", detectResourceConflicts());

        return Result.success(diagnosis);
    }

    /**
     * 接待数据一致性检查
     */
    @GetMapping("/consistency")
    public Result<Map<String, Object>> receptionConsistencyCheck() {
        Map<String, Object> consistency = new HashMap<>();

        // 检查申请与流程数据一致性
        List<String> inconsistentApplications = findInconsistentReceptionApplications();
        consistency.put("inconsistentApplications", inconsistentApplications);

        // 检查资源分配一致性
        List<String> inconsistentAllocations = findInconsistentResourceAllocations();
        consistency.put("inconsistentAllocations", inconsistentAllocations);

        // 检查费用计算一致性
        List<String> inconsistentCosts = findInconsistentCostCalculations();
        consistency.put("inconsistentCosts", inconsistentCosts);

        // 检查外部系统数据一致性
        List<String> inconsistentExternalData = findInconsistentExternalSystemData();
        consistency.put("inconsistentExternalData", inconsistentExternalData);

        return Result.success(consistency);
    }

    /**
     * 修复接待数据不一致
     */
    @PostMapping("/repair")
    public Result<String> repairReceptionDataInconsistency(@RequestBody ReceptionRepairRequest request) {
        try {
            switch (request.getType()) {
                case "application_status":
                    repairApplicationStatus(request.getIds());
                    break;
                case "resource_allocation":
                    repairResourceAllocation(request.getIds());
                    break;
                case "cost_calculation":
                    repairCostCalculation(request.getIds());
                    break;
                case "external_system_sync":
                    repairExternalSystemSync(request.getIds());
                    break;
                default:
                    return Result.error("未知的修复类型");
            }

            return Result.success("修复完成");
        } catch (Exception e) {
            log.error("接待数据修复失败", e);
            return Result.error("修复失败：" + e.getMessage());
        }
    }

    private Map<String, Object> checkReceptionDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            int applicationCount = applicationService.count();
            int accommodationCount = accommodationService.count();
            long responseTime = System.currentTimeMillis() - startTime;

            dbHealth.put("status", "UP");
            dbHealth.put("responseTime", responseTime);
            dbHealth.put("applicationCount", applicationCount);
            dbHealth.put("accommodationCount", accommodationCount);

        } catch (Exception e) {
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }

        return dbHealth;
    }

    private Map<String, Object> analyzeRoomResources() {
        Map<String, Object> analysis = new HashMap<>();

        // 统计各类型房间使用情况
        List<RoomUsageStats> usageStats = calculateRoomUsageStats();
        analysis.put("usageStats", usageStats);

        // 识别资源瓶颈
        List<String> bottleneckRoomTypes = identifyBottleneckRoomTypes();
        analysis.put("bottlenecks", bottleneckRoomTypes);

        // 预测资源需求
        Map<String, Object> demandForecast = forecastRoomDemand();
        analysis.put("demandForecast", demandForecast);

        return analysis;
    }
}
```

**2. 应急处理预案**

```java
/**
 * 接待管理应急处理服务
 */
@Service
@Slf4j
public class ReceptionEmergencyService {

    @Resource
    private ReceptionNotificationService notificationService;

    @Resource
    private ReceptionApplicationService applicationService;

    /**
     * 接待系统降级处理
     */
    public void activateReceptionSystemDegradation(DegradationLevel level) {
        switch (level) {
            case LEVEL_1: // 轻度降级
                // 关闭非核心功能
                disableNonCriticalReceptionFeatures();
                break;

            case LEVEL_2: // 中度降级
                // 启用简化资源分配模式
                enableSimplifiedResourceAllocation();
                break;

            case LEVEL_3: // 重度降级
                // 启用手动资源分配模式
                enableManualResourceAllocation();
                break;

            case EMERGENCY: // 紧急模式
                // 系统维护模式
                enableReceptionMaintenanceMode();
                break;
        }

        // 通知相关人员
        notifyReceptionEmergencyResponse(level);
    }

    /**
     * 接待资源分配故障自动恢复
     */
    @EventListener
    public void handleResourceAllocationFailure(ResourceAllocationFailureEvent event) {
        try {
            // 记录故障信息
            recordReceptionFailureInfo(event);

            // 尝试自动恢复
            boolean recovered = attemptReceptionAutoRecovery(event.getFailureType());

            if (recovered) {
                log.info("接待资源分配故障自动恢复成功：{}", event.getFailureType());
                notifyReceptionRecoverySuccess(event);
            } else {
                log.error("接待资源分配故障自动恢复失败：{}", event.getFailureType());
                escalateToReceptionManualIntervention(event);
            }

        } catch (Exception e) {
            log.error("接待故障处理异常", e);
            escalateToReceptionManualIntervention(event);
        }
    }

    /**
     * 紧急接待资源调度
     */
    public ReceptionEmergencyAllocationResult emergencyResourceAllocation(ReceptionEmergencyRequest request) {
        try {
            // 1. 获取所有可用资源（包括预约中的）
            List<AccommodationRoomDO> allRooms = getAllAvailableRooms();

            // 2. 紧急调度算法（优先级更高）
            ResourceAllocationResult result = executeEmergencyAllocation(allRooms, request);

            // 3. 如果需要，取消低优先级预约
            if (!result.isSuccess()) {
                result = allocateWithCancellation(request);
            }

            // 4. 通知相关人员
            notifyEmergencyAllocationResult(result, request);

            return ReceptionEmergencyAllocationResult.fromAllocationResult(result);

        } catch (Exception e) {
            log.error("紧急接待资源调度失败", e);
            return ReceptionEmergencyAllocationResult.error("紧急调度失败");
        }
    }

    /**
     * 外部系统故障应急处理
     */
    public void handleExternalSystemFailure(String systemName, String failureType) {
        switch (systemName) {
            case "ACCOMMODATION_SYSTEM":
                handleAccommodationSystemFailure(failureType);
                break;
            case "MEAL_CARD_SYSTEM":
                handleMealCardSystemFailure(failureType);
                break;
            case "FACE_RECOGNITION_SYSTEM":
                handleFaceRecognitionSystemFailure(failureType);
                break;
            default:
                log.warn("未知的外部系统故障：{}", systemName);
        }
    }

    /**
     * 住宿系统故障处理
     */
    private void handleAccommodationSystemFailure(String failureType) {
        // 1. 启用本地房间管理模式
        enableLocalRoomManagement();

        // 2. 暂停自动房间分配
        pauseAutomaticRoomAllocation();

        // 3. 通知管理员手动处理
        notifyAccommodationSystemFailure(failureType);

        // 4. 记录故障日志
        recordSystemFailure("ACCOMMODATION_SYSTEM", failureType);
    }

    /**
     * 饭卡系统故障处理
     */
    private void handleMealCardSystemFailure(String failureType) {
        // 1. 启用临时就餐登记模式
        enableTemporaryDiningRegistration();

        // 2. 暂停饭卡申领
        pauseMealCardIssuance();

        // 3. 启用人脸识别备用方案
        enableFaceRecognitionBackup();

        // 4. 通知餐厅管理员
        notifyMealCardSystemFailure(failureType);
    }

    /**
     * 人脸识别系统故障处理
     */
    private void handleFaceRecognitionSystemFailure(String failureType) {
        // 1. 切换到饭卡模式
        switchToMealCardMode();

        // 2. 暂停人脸注册
        pauseFaceRegistration();

        // 3. 启用手动就餐登记
        enableManualDiningRegistration();

        // 4. 通知相关人员
        notifyFaceRecognitionSystemFailure(failureType);
    }

    private boolean attemptReceptionAutoRecovery(String failureType) {
        switch (failureType) {
            case "RESOURCE_ALLOCATION_FAILED":
                return recoverResourceAllocation();

            case "COST_CALCULATION_FAILED":
                return recoverCostCalculation();

            case "EXTERNAL_SYSTEM_TIMEOUT":
                return recoverExternalSystemConnection();

            case "CACHE_SERVICE_FAILED":
                return recoverReceptionCacheService();

            default:
                return false;
        }
    }

    private void escalateToReceptionManualIntervention(ResourceAllocationFailureEvent event) {
        // 发送紧急通知
        notificationService.sendReceptionEmergencyAlert(
            "接待系统故障需要人工干预",
            "故障类型：" + event.getFailureType() + "\n" +
            "故障时间：" + event.getTimestamp() + "\n" +
            "故障描述：" + event.getDescription()
        );

        // 记录到故障处理系统
        recordReceptionManualInterventionRequired(event);
    }
}
```

## 前端页面设计

### 页面结构设计

#### 1. 接待申请页面

**页面路径：** `/reception/application/create`

**页面组件结构：**

```vue
<template>
  <div class="reception-application-page">
    <!-- 页面头部 -->
    <page-header title="接待申请" :breadcrumb="breadcrumbItems" />

    <!-- 步骤指示器 -->
    <step-indicator :current="currentStep" :steps="steps" />

    <!-- 申请表单 -->
    <div class="application-form-container">
      <el-form ref="applicationForm" :model="formData" :rules="formRules" label-width="120px">

        <!-- 步骤1：关联访客申请 -->
        <div v-show="currentStep === 1" class="form-step">
          <div class="step-title">关联访客申请</div>
          <el-form-item label="选择访客申请" prop="visitorApplicationId" required>
            <visitor-application-selector
              v-model="formData.visitorApplicationId"
              @change="onVisitorApplicationChange" />
          </el-form-item>

          <div v-if="selectedVisitorApplication" class="visitor-info-preview">
            <h4>访客信息预览</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>申请单号：</label>
                <span>{{ selectedVisitorApplication.applicationNo }}</span>
              </div>
              <div class="info-item">
                <label>访客姓名：</label>
                <span>{{ selectedVisitorApplication.visitorName }}</span>
              </div>
              <div class="info-item">
                <label>访客单位：</label>
                <span>{{ selectedVisitorApplication.companyName }}</span>
              </div>
              <div class="info-item">
                <label>访问时间：</label>
                <span>{{ selectedVisitorApplication.visitStartTime }} - {{ selectedVisitorApplication.visitEndTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2：服务配置 -->
        <div v-show="currentStep === 2" class="form-step">
          <div class="step-title">配置接待服务</div>

          <!-- 接待类型选择 -->
          <el-form-item label="接待类型" prop="receptionType" required>
            <el-radio-group v-model="formData.receptionType" @change="onReceptionTypeChange">
              <el-radio-button :label="1">仅住宿</el-radio-button>
              <el-radio-button :label="2">仅就餐</el-radio-button>
              <el-radio-button :label="3">住宿+就餐</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 接待级别 -->
          <el-form-item label="接待级别" prop="receptionLevel">
            <el-select v-model="formData.receptionLevel" placeholder="请选择接待级别">
              <el-option label="普通接待" :value="1" />
              <el-option label="重要接待" :value="2" />
              <el-option label="VIP接待" :value="3" />
            </el-select>
          </el-form-item>

          <!-- 住宿服务配置 -->
          <div v-if="needAccommodation" class="service-section">
            <h4>住宿服务配置</h4>

            <el-form-item label="入住时间" required>
              <el-col :span="11">
                <el-form-item prop="checkInDate">
                  <el-date-picker
                    v-model="formData.checkInDate"
                    type="date"
                    placeholder="入住日期"
                    @change="onAccommodationDateChange" />
                </el-form-item>
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="form-text">至</span>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="checkOutDate">
                  <el-date-picker
                    v-model="formData.checkOutDate"
                    type="date"
                    placeholder="退宿日期"
                    @change="onAccommodationDateChange" />
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item label="房间类型" prop="roomType">
              <el-select v-model="formData.roomType" placeholder="请选择房间类型">
                <el-option label="标准间" :value="1" />
                <el-option label="单人间" :value="2" />
                <el-option label="套房" :value="3" />
              </el-select>
            </el-form-item>

            <el-form-item label="房间数量" prop="roomCount">
              <el-input-number v-model="formData.roomCount" :min="1" :max="10" />
            </el-form-item>

            <!-- 房间可用性状态 -->
            <div id="roomAvailabilityStatus" class="availability-status-container"></div>
          </div>

          <!-- 就餐服务配置 -->
          <div v-if="needDining" class="service-section">
            <h4>就餐服务配置</h4>

            <el-form-item label="就餐时间" required>
              <el-col :span="11">
                <el-form-item prop="diningStartDate">
                  <el-date-picker
                    v-model="formData.diningStartDate"
                    type="date"
                    placeholder="开始日期" />
                </el-form-item>
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="form-text">至</span>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="diningEndDate">
                  <el-date-picker
                    v-model="formData.diningEndDate"
                    type="date"
                    placeholder="结束日期" />
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item label="用餐标准" prop="mealStandard">
              <el-select v-model="formData.mealStandard" placeholder="请选择用餐标准">
                <el-option label="普通标准" :value="1" />
                <el-option label="商务标准" :value="2" />
                <el-option label="高级标准" :value="3" />
              </el-select>
            </el-form-item>

            <div class="meal-count-section">
              <el-form-item label="早餐人次" prop="breakfastCount">
                <el-input-number v-model="formData.breakfastCount" :min="0" />
              </el-form-item>
              <el-form-item label="午餐人次" prop="lunchCount">
                <el-input-number v-model="formData.lunchCount" :min="0" />
              </el-form-item>
              <el-form-item label="晚餐人次" prop="dinnerCount">
                <el-input-number v-model="formData.dinnerCount" :min="0" />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 步骤3：费用确认 -->
        <div v-show="currentStep === 3" class="form-step">
          <div class="step-title">确认服务费用</div>

          <!-- 费用明细 -->
          <div id="costBreakdown" class="cost-breakdown-container"></div>

          <el-form-item label="费用承担方" prop="costBearer">
            <el-radio-group v-model="formData.costBearer">
              <el-radio :label="1">公司承担</el-radio>
              <el-radio :label="2">访客自付</el-radio>
              <el-radio :label="3">部门承担</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 步骤4：提交申请 -->
        <div v-show="currentStep === 4" class="form-step">
          <div class="step-title">确认申请信息</div>
          <reception-application-summary :form-data="formData" />
        </div>

      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button v-if="currentStep > 1" @click="prevStep">上一步</el-button>
      <el-button v-if="currentStep < 4" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="currentStep === 4" type="primary" :loading="submitting" @click="submitApplication">
        提交申请
      </el-button>
    </div>
  </div>
</template>
```

## 测试用例设计

### 单元测试用例

#### 1. 资源分配算法测试

```java
@SpringBootTest
class ReceptionResourceAllocationServiceTest {

    @Resource
    private ReceptionResourceAllocationService allocationService;

    @Test
    @DisplayName("测试房间分配算法 - 正常场景")
    void testRoomAllocation_Normal() {
        // Given
        ReceptionApplicationDO application = createTestApplication();

        // When
        RoomAllocationResult result = allocationService.allocateRooms(application);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getAllocatedRooms()).isNotEmpty();
        assertThat(result.getAllocatedRooms().size()).isEqualTo(application.getRoomCount());
    }

    @Test
    @DisplayName("测试房间分配算法 - 无可用房间")
    void testRoomAllocation_NoAvailableRooms() {
        // Given
        ReceptionApplicationDO application = createTestApplicationWithConflictDates();

        // When
        RoomAllocationResult result = allocationService.allocateRooms(application);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getMessage()).contains("可用房间不足");
    }
}
```
