# 访客管理模块详细设计文档

## 文档信息

| 项目名称 | 访客管理模块 |
|---------|-------------|
| 模块标识 | yudao-module-visitor |
| 文档版本 | 2.0 |
| 编写日期 | 2025-08-11 |
| 编写人员 | 边浩澜 |

## 目录

1. [模块概述](#模块概述)
2. [数据库设计](#数据库设计)
3. [API接口设计](#API接口设计)
4. [业务流程设计](#业务流程设计)
5. [前端页面设计](#前端页面设计)
6. [集成方案设计](#集成方案设计)
7. [测试用例设计](#测试用例设计)

## 模块概述

### 业务范围
访客管理模块负责园区访客的全生命周期管理，包括访客申请、审批、培训、入园、出园等完整流程。支持普通访客、政府访客、施工承包商三种访客类型的差异化管理。

### 核心功能

- **访客申请管理**：在线申请、信息填写、资料上传
- **审批流程管理**：多级审批、流程配置、状态跟踪
- **安全培训管理**：在线培训、考试测评、证书管理
- **二维码通行**：动态生成、安全验证、权限控制
- **进出记录管理**：实时记录、异常处理、数据统计
- **通知提醒服务**：企业微信、短信、邮件多渠道通知

### 技术架构

```mermaid
graph TB
    subgraph "访客管理模块架构"
        A1[PC管理端] --> B1[Admin Controller]
        A2[H5移动端] --> B2[App Controller]
        
        B1 --> C1[Visitor Service]
        B2 --> C1
        
        C1 --> D1[Training Service]
        C1 --> D2[QrCode Service]
        C1 --> D3[Notification Service]
        
        D1 --> E1[Visitor Mapper]
        D2 --> E2[Training Mapper]
        D3 --> E3[Record Mapper]
        
        E1 --> F1[visitor_application]
        E2 --> F2[visitor_training]
        E3 --> F3[visitor_record]
        
        C1 --> G1[Flowable Engine]
        D3 --> G2[WeChat API]
        D3 --> G3[SMS Service]
    end
```

### 模块结构

```text
yudao-module-visitor/
├── pom.xml
├── yudao-module-visitor-api/
│   ├── src/main/java/cn/iocoder/yudao/module/visitor/
│   │   ├── api/
│   │   │   ├── VisitorApplicationApi.java
│   │   │   └── VisitorRecordApi.java
│   │   ├── enums/
│   │   │   ├── VisitorTypeEnum.java
│   │   │   ├── VisitorStatusEnum.java
│   │   │   └── TrainingStatusEnum.java
│   │   └── dto/
│   │       ├── VisitorApplicationDTO.java
│   │       └── VisitorRecordDTO.java
│   └── pom.xml
└── yudao-module-visitor-biz/
    ├── src/main/java/cn/iocoder/yudao/module/visitor/
    │   ├── controller/
    │   │   ├── admin/
    │   │   │   ├── VisitorApplicationController.java
    │   │   │   ├── VisitorRecordController.java
    │   │   │   └── VisitorTrainingController.java
    │   │   └── app/
    │   │       ├── AppVisitorApplicationController.java
    │   │       ├── AppVisitorTrainingController.java
    │   │       └── AppGuardController.java
    │   ├── service/
    │   │   ├── application/
    │   │   │   ├── VisitorApplicationService.java
    │   │   │   └── VisitorApplicationServiceImpl.java
    │   │   ├── record/
    │   │   │   ├── VisitorRecordService.java
    │   │   │   └── VisitorRecordServiceImpl.java
    │   │   └── training/
    │   │       ├── VisitorTrainingService.java
    │   │       └── VisitorTrainingServiceImpl.java
    │   ├── dal/
    │   │   ├── dataobject/
    │   │   │   ├── VisitorApplicationDO.java
    │   │   │   ├── VisitorRecordDO.java
    │   │   │   ├── VisitorTrainingDO.java
    │   │   │   └── VisitorTrainingRecordDO.java
    │   │   └── mysql/
    │   │       ├── VisitorApplicationMapper.java
    │   │       ├── VisitorRecordMapper.java
    │   │       ├── VisitorTrainingMapper.java
    │   │       └── VisitorTrainingRecordMapper.java
    │   ├── convert/
    │   │   ├── VisitorApplicationConvert.java
    │   │   ├── VisitorRecordConvert.java
    │   │   └── VisitorTrainingConvert.java
    │   ├── job/
    │   │   ├── VisitorExpireJob.java
    │   │   └── VisitorStatisticsJob.java
    │   └── framework/
    │       ├── qrcode/
    │       │   └── VisitorQrCodeService.java
    │       ├── notification/
    │       │   └── VisitorNotificationService.java
    │       └── flowable/
    │           ├── listener/
    │           └── delegate/
    ├── src/main/resources/
    │   ├── mapper/
    │   │   ├── VisitorApplicationMapper.xml
    │   │   ├── VisitorRecordMapper.xml
    │   │   └── VisitorTrainingMapper.xml
    │   └── processes/
    │       ├── visitor_normal_approval.bpmn20.xml
    │       ├── visitor_government_approval.bpmn20.xml
    │       └── visitor_contractor_approval.bpmn20.xml
    └── pom.xml
```

## 数据库设计

### 核心表结构

#### 1. 访客申请表 (visitor_application)

```sql
CREATE TABLE visitor_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_no VARCHAR(32) NOT NULL COMMENT '申请单号',
    visitor_type TINYINT NOT NULL COMMENT '访客类型：1-普通访客 2-政府访客 3-施工承包商',
    company_name VARCHAR(100) NOT NULL COMMENT '来访单位',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    visitor_phone VARCHAR(20) NOT NULL COMMENT '联系方式',
    id_card VARCHAR(18) COMMENT '身份证号码',
    visitor_photo VARCHAR(500) COMMENT '访客照片URL',
    visit_reason VARCHAR(200) NOT NULL COMMENT '来访事由',
    visit_purpose TINYINT COMMENT '访问目的：1-参观 2-商务 3-施工 4-检查',
    contact_person VARCHAR(50) NOT NULL COMMENT '厂内联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系人电话',
    contact_dept_id BIGINT COMMENT '联系人部门ID',
    contact_dept_name VARCHAR(100) COMMENT '联系人部门名称',
    visit_start_time DATETIME NOT NULL COMMENT '预计到访开始时间',
    visit_end_time DATETIME NOT NULL COMMENT '预计到访结束时间',
    visit_area VARCHAR(50) NOT NULL COMMENT '到访厂区',
    visit_method TINYINT NOT NULL COMMENT '到访方式：1-随车入园 2-各自入园',
    has_vehicle TINYINT DEFAULT 0 COMMENT '是否驾车：0-否 1-是',
    vehicle_plate VARCHAR(20) COMMENT '车牌号',
    vehicle_type TINYINT COMMENT '车辆类型：1-普通客车 2-施工车',
    vehicle_license VARCHAR(100) COMMENT '行驶证编号',
    vehicle_photo VARCHAR(500) COMMENT '车辆照片URL',
    companion_count INT DEFAULT 0 COMMENT '同行人数量',
    companion_info JSON COMMENT '同行人信息JSON：[{name,phone,idCard,photo}]',
    need_accommodation TINYINT DEFAULT 0 COMMENT '是否需要住宿：0-否 1-是',
    need_dining TINYINT DEFAULT 0 COMMENT '是否前往饭堂就餐：0-否 1-是',
    training_completed TINYINT DEFAULT 0 COMMENT '是否完成培训：0-否 1-是',
    training_time DATETIME COMMENT '培训完成时间',
    training_signature VARCHAR(500) COMMENT '培训签字图片URL',
    qr_code VARCHAR(500) COMMENT '通行二维码URL',
    qr_code_content TEXT COMMENT '二维码内容（JWT Token）',
    qr_code_expire_time DATETIME COMMENT '二维码过期时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待确认 2-已审批 3-已驳回 4-已入园 5-已出园',
    approval_result TINYINT COMMENT '审批结果：1-通过 2-驳回',
    approval_reason VARCHAR(200) COMMENT '审批意见',
    approval_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(50) COMMENT '审批人姓名',
    entry_time DATETIME COMMENT '入园时间',
    exit_time DATETIME COMMENT '出园时间',
    entry_operator_id BIGINT COMMENT '入园操作员ID',
    exit_operator_id BIGINT COMMENT '出园操作员ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    contractor_project_id BIGINT COMMENT '承包商项目ID（施工承包商专用）',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    special_requirements VARCHAR(500) COMMENT '特殊要求',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    UNIQUE KEY uk_application_no (application_no, deleted),
    INDEX idx_visitor_type (visitor_type),
    INDEX idx_visitor_phone (visitor_phone),
    INDEX idx_contact_person (contact_person),
    INDEX idx_contact_dept_id (contact_dept_id),
    INDEX idx_status (status),
    INDEX idx_visit_time (visit_start_time, visit_end_time),
    INDEX idx_create_time (create_time),
    INDEX idx_process_instance (process_instance_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客申请表';
```

#### 2. 访客进出记录表 (visitor_record)

```sql
CREATE TABLE visitor_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    visitor_phone VARCHAR(20) COMMENT '访客电话',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-入园 2-出园',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    gate_location VARCHAR(100) COMMENT '门岗位置',
    verification_method TINYINT COMMENT '验证方式：1-二维码 2-手动 3-人脸识别',
    vehicle_plate VARCHAR(20) COMMENT '车牌号',
    vehicle_photo VARCHAR(500) COMMENT '车辆照片URL',
    visitor_photo VARCHAR(500) COMMENT '现场访客照片URL',
    abnormal_info VARCHAR(500) COMMENT '异常情况描述',
    abnormal_photos JSON COMMENT '异常照片URL数组',
    temperature DECIMAL(4,1) COMMENT '体温（摄氏度）',
    health_status TINYINT COMMENT '健康状态：1-正常 2-异常',
    security_check_result TINYINT COMMENT '安检结果：1-通过 2-未通过',
    remarks VARCHAR(500) COMMENT '备注信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_time (operation_time),
    INDEX idx_operator_id (operator_id),
    INDEX idx_vehicle_plate (vehicle_plate),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客进出记录表';
```

#### 3. 访客培训配置表 (visitor_training)

```sql
CREATE TABLE visitor_training (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    training_name VARCHAR(100) NOT NULL COMMENT '培训名称',
    training_type TINYINT NOT NULL COMMENT '培训类型：1-安全培训 2-操作培训 3-规章制度 4-应急预案',
    training_content TEXT COMMENT '培训内容',
    training_materials JSON COMMENT '培训材料URL数组：[{type:pdf/video/image,url,name,size}]',
    min_duration INT COMMENT '最小培训时长（分钟）',
    max_duration INT COMMENT '最大培训时长（分钟）',
    is_required TINYINT DEFAULT 1 COMMENT '是否必修：0-否 1-是',
    visitor_type TINYINT COMMENT '适用访客类型：1-普通访客 2-政府访客 3-施工承包商 0-全部',
    has_exam TINYINT DEFAULT 0 COMMENT '是否有考试：0-否 1-是',
    exam_questions JSON COMMENT '考试题目JSON',
    pass_score INT DEFAULT 80 COMMENT '及格分数',
    certificate_template VARCHAR(500) COMMENT '证书模板URL',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_training_type (training_type),
    INDEX idx_visitor_type (visitor_type),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客培训配置表';
```

#### 4. 访客培训完成记录表 (visitor_training_record)

```sql
CREATE TABLE visitor_training_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    training_id BIGINT NOT NULL COMMENT '培训ID',
    visitor_name VARCHAR(50) NOT NULL COMMENT '访客姓名',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration INT COMMENT '实际培训时长（分钟）',
    completion_status TINYINT NOT NULL DEFAULT 0 COMMENT '完成状态：0-未完成 1-已完成 2-已过期',
    exam_score INT COMMENT '考试得分',
    exam_answers JSON COMMENT '考试答案JSON',
    signature_image VARCHAR(500) COMMENT '签字图片URL',
    certificate_url VARCHAR(500) COMMENT '证书URL',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    remarks VARCHAR(200) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_training_id (training_id),
    INDEX idx_completion_status (completion_status),
    INDEX idx_start_time (start_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客培训完成记录表';
```

#### 5. 流程操作记录表 (visitor_process_operation)

```sql
CREATE TABLE visitor_process_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    task_name VARCHAR(100) COMMENT '任务名称',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-提交申请 2-联系人确认 3-部门审批 4-综管部审批 5-安全部审批 6-生成二维码 7-入园登记 8-出园登记 9-流程取消 10-流程驳回',
    operation_result TINYINT COMMENT '操作结果：1-通过 2-驳回 3-取消 4-退回修改',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    operator_type TINYINT COMMENT '操作人类型：1-申请人 2-联系人 3-审批人 4-警卫 5-系统自动',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    operation_duration INT COMMENT '操作耗时（分钟）',
    operation_content TEXT COMMENT '操作内容描述',
    operation_reason VARCHAR(500) COMMENT '操作原因/备注',
    before_status TINYINT COMMENT '操作前状态',
    after_status TINYINT COMMENT '操作后状态',
    form_data JSON COMMENT '表单数据快照',
    attachment_urls JSON COMMENT '附件URL数组',
    ip_address VARCHAR(50) COMMENT '操作IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理信息',
    device_info VARCHAR(200) COMMENT '设备信息',
    location_info VARCHAR(200) COMMENT '位置信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator_id (operator_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客流程操作记录表';
```

#### 6. 流程跟进记录表 (visitor_process_follow)

```sql
CREATE TABLE visitor_process_follow (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    follow_type TINYINT NOT NULL COMMENT '跟进类型：1-电话沟通 2-企业微信 3-邮件沟通 4-现场沟通 5-短信通知 6-系统提醒 7-其他',
    follow_direction TINYINT NOT NULL COMMENT '沟通方向：1-主动跟进 2-被动响应',
    follow_person_id BIGINT COMMENT '跟进人ID',
    follow_person_name VARCHAR(50) COMMENT '跟进人姓名',
    follow_person_role TINYINT COMMENT '跟进人角色：1-申请人 2-联系人 3-审批人 4-警卫 5-系统管理员',
    target_person_id BIGINT COMMENT '目标对象ID',
    target_person_name VARCHAR(50) COMMENT '目标对象姓名',
    target_person_role TINYINT COMMENT '目标对象角色：1-申请人 2-联系人 3-审批人 4-警卫 5-系统管理员',
    follow_time DATETIME NOT NULL COMMENT '跟进时间',
    follow_duration INT COMMENT '跟进时长（分钟）',
    follow_subject VARCHAR(200) COMMENT '跟进主题',
    follow_content TEXT NOT NULL COMMENT '跟进内容详情',
    follow_result TINYINT COMMENT '跟进结果：1-问题解决 2-需要进一步跟进 3-转交他人处理 4-无需处理',
    next_follow_time DATETIME COMMENT '下次跟进时间',
    next_follow_person_id BIGINT COMMENT '下次跟进人ID',
    attachment_urls JSON COMMENT '附件URL数组（录音、截图等）',
    related_task_id VARCHAR(64) COMMENT '关联的任务ID',
    urgency_level TINYINT DEFAULT 2 COMMENT '紧急程度：1-紧急 2-普通 3-不急',
    is_resolved TINYINT DEFAULT 0 COMMENT '是否已解决：0-未解决 1-已解决',
    resolution_time DATETIME COMMENT '解决时间',
    satisfaction_score TINYINT COMMENT '满意度评分（1-5分）',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_follow_type (follow_type),
    INDEX idx_follow_person_id (follow_person_id),
    INDEX idx_target_person_id (target_person_id),
    INDEX idx_follow_time (follow_time),
    INDEX idx_next_follow_time (next_follow_time),
    INDEX idx_urgency_level (urgency_level),
    INDEX idx_is_resolved (is_resolved),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客流程跟进记录表';
```

#### 7. 流程状态变更历史表 (visitor_status_history)

```sql
CREATE TABLE visitor_status_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    change_sequence INT NOT NULL COMMENT '变更序号',
    before_status TINYINT COMMENT '变更前状态',
    after_status TINYINT NOT NULL COMMENT '变更后状态',
    status_name VARCHAR(50) COMMENT '状态名称',
    change_reason VARCHAR(200) COMMENT '变更原因',
    change_type TINYINT NOT NULL COMMENT '变更类型：1-正常流转 2-人工干预 3-系统自动 4-异常处理 5-流程回退',
    change_trigger TINYINT COMMENT '变更触发方式：1-用户操作 2-定时任务 3-系统事件 4-外部接口',
    change_person_id BIGINT COMMENT '变更操作人ID',
    change_person_name VARCHAR(50) COMMENT '变更操作人姓名',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    duration_minutes INT COMMENT '在前一状态停留时长（分钟）',
    related_task_id VARCHAR(64) COMMENT '关联的任务ID',
    related_operation_id BIGINT COMMENT '关联的操作记录ID',
    business_data JSON COMMENT '业务数据快照',
    system_variables JSON COMMENT '系统变量快照',
    notification_sent TINYINT DEFAULT 0 COMMENT '是否已发送通知：0-否 1-是',
    notification_time DATETIME COMMENT '通知发送时间',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_change_sequence (application_id, change_sequence),
    INDEX idx_before_status (before_status),
    INDEX idx_after_status (after_status),
    INDEX idx_change_type (change_type),
    INDEX idx_change_person_id (change_person_id),
    INDEX idx_change_time (change_time),
    INDEX idx_related_task_id (related_task_id),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客状态变更历史表';
```

#### 8. 流程审批意见表 (visitor_approval_opinion)

```sql
CREATE TABLE visitor_approval_opinion (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT 'Flowable任务ID',
    task_definition_key VARCHAR(100) COMMENT '任务定义Key',
    approval_level TINYINT NOT NULL COMMENT '审批级别：1-联系人确认 2-部门主管审批 3-综管部审批 4-安全部审批',
    approval_sequence INT COMMENT '审批序号（同级多人审批时使用）',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(50) NOT NULL COMMENT '审批人姓名',
    approver_dept_id BIGINT COMMENT '审批人部门ID',
    approver_dept_name VARCHAR(100) COMMENT '审批人部门名称',
    approver_role VARCHAR(50) COMMENT '审批人角色',
    approval_type TINYINT NOT NULL COMMENT '审批类型：1-单人审批 2-会签 3-或签 4-依次审批',
    approval_result TINYINT NOT NULL COMMENT '审批结果：1-通过 2-驳回 3-退回修改 4-转交他人 5-加签',
    approval_time DATETIME NOT NULL COMMENT '审批时间',
    approval_duration INT COMMENT '审批耗时（分钟）',
    opinion_type TINYINT COMMENT '意见类型：1-同意 2-有条件同意 3-不同意 4-建议修改',
    opinion_content TEXT COMMENT '审批意见内容',
    opinion_summary VARCHAR(200) COMMENT '意见摘要',
    conditions_requirements TEXT COMMENT '附加条件或要求',
    risk_assessment TEXT COMMENT '风险评估意见',
    security_suggestions TEXT COMMENT '安全建议',
    attachment_urls JSON COMMENT '审批附件URL数组',
    signature_image VARCHAR(500) COMMENT '电子签名图片URL',
    delegate_person_id BIGINT COMMENT '被委托人ID（转交时使用）',
    delegate_reason VARCHAR(200) COMMENT '委托原因',
    is_final_approval TINYINT DEFAULT 0 COMMENT '是否为最终审批：0-否 1-是',
    next_approver_suggestion VARCHAR(200) COMMENT '下级审批建议',
    approval_weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '审批权重（会签时使用）',
    ip_address VARCHAR(50) COMMENT '审批IP地址',
    device_info VARCHAR(200) COMMENT '审批设备信息',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_task_id (task_id),
    INDEX idx_approval_level (approval_level),
    INDEX idx_approver_id (approver_id),
    INDEX idx_approval_result (approval_result),
    INDEX idx_approval_time (approval_time),
    INDEX idx_is_final_approval (is_final_approval),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客流程审批意见表';
```

#### 9. 流程异常处理记录表 (visitor_process_exception)

```sql
CREATE TABLE visitor_process_exception (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) COMMENT 'Flowable流程实例ID',
    task_id VARCHAR(64) COMMENT '异常任务ID',
    exception_type TINYINT NOT NULL COMMENT '异常类型：1-超时未处理 2-系统错误 3-数据异常 4-权限异常 5-业务规则异常 6-外部服务异常 7-人工干预 8-流程中断 9-其他异常',
    exception_level TINYINT NOT NULL COMMENT '异常级别：1-低 2-中 3-高 4-紧急',
    exception_code VARCHAR(50) COMMENT '异常代码',
    exception_title VARCHAR(200) NOT NULL COMMENT '异常标题',
    exception_description TEXT NOT NULL COMMENT '异常详细描述',
    exception_context JSON COMMENT '异常上下文信息',
    exception_stack_trace TEXT COMMENT '异常堆栈信息',
    exception_time DATETIME NOT NULL COMMENT '异常发生时间',
    detection_method TINYINT COMMENT '发现方式：1-系统自动检测 2-用户反馈 3-监控告警 4-人工发现',
    detector_id BIGINT COMMENT '发现人ID',
    detector_name VARCHAR(50) COMMENT '发现人姓名',
    affected_users JSON COMMENT '受影响用户列表',
    business_impact TEXT COMMENT '业务影响描述',
    handling_status TINYINT DEFAULT 1 COMMENT '处理状态：1-待处理 2-处理中 3-已解决 4-已关闭 5-无需处理',
    handler_id BIGINT COMMENT '处理人ID',
    handler_name VARCHAR(50) COMMENT '处理人姓名',
    handling_start_time DATETIME COMMENT '开始处理时间',
    handling_end_time DATETIME COMMENT '处理完成时间',
    handling_duration INT COMMENT '处理耗时（分钟）',
    handling_method TINYINT COMMENT '处理方式：1-系统自动修复 2-人工处理 3-流程重启 4-数据修正 5-配置调整 6-其他',
    handling_steps TEXT COMMENT '处理步骤记录',
    handling_result TEXT COMMENT '处理结果描述',
    resolution_type TINYINT COMMENT '解决方案类型：1-临时解决 2-永久解决 3-规避方案 4-无法解决',
    prevention_measures TEXT COMMENT '预防措施',
    related_ticket_no VARCHAR(100) COMMENT '关联工单号',
    escalation_level TINYINT DEFAULT 0 COMMENT '升级级别：0-未升级 1-一级升级 2-二级升级 3-三级升级',
    escalation_time DATETIME COMMENT '升级时间',
    escalation_reason VARCHAR(200) COMMENT '升级原因',
    notification_sent TINYINT DEFAULT 0 COMMENT '是否已发送通知：0-否 1-是',
    notification_recipients JSON COMMENT '通知接收人列表',
    follow_up_required TINYINT DEFAULT 0 COMMENT '是否需要后续跟进：0-否 1-是',
    follow_up_time DATETIME COMMENT '跟进时间',
    follow_up_content TEXT COMMENT '跟进内容',
    lessons_learned TEXT COMMENT '经验教训总结',
    attachment_urls JSON COMMENT '相关附件URL数组',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    INDEX idx_application_id (application_id),
    INDEX idx_process_instance_id (process_instance_id),
    INDEX idx_exception_type (exception_type),
    INDEX idx_exception_level (exception_level),
    INDEX idx_exception_time (exception_time),
    INDEX idx_handling_status (handling_status),
    INDEX idx_handler_id (handler_id),
    INDEX idx_escalation_level (escalation_level),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客流程异常处理记录表';
```

### Flowable工作流引擎集成设计

#### 1. 流程实例与业务数据关联

##### 业务流程实例扩展表 (visitor_process_instance_ext)

```sql
CREATE TABLE visitor_process_instance_ext (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_id BIGINT NOT NULL COMMENT '申请单ID',
    process_instance_id VARCHAR(64) NOT NULL COMMENT 'Flowable流程实例ID',
    process_definition_id VARCHAR(64) NOT NULL COMMENT '流程定义ID',
    process_definition_key VARCHAR(100) NOT NULL COMMENT '流程定义Key',
    process_definition_name VARCHAR(200) COMMENT '流程定义名称',
    process_definition_version INT COMMENT '流程定义版本',
    business_key VARCHAR(100) NOT NULL COMMENT '业务Key（申请单号）',
    process_name VARCHAR(200) COMMENT '流程实例名称',
    process_category VARCHAR(50) COMMENT '流程分类',
    start_user_id BIGINT NOT NULL COMMENT '发起人ID',
    start_user_name VARCHAR(50) COMMENT '发起人姓名',
    start_time DATETIME NOT NULL COMMENT '流程开始时间',
    end_time DATETIME COMMENT '流程结束时间',
    duration_millis BIGINT COMMENT '流程持续时间（毫秒）',
    process_status TINYINT NOT NULL COMMENT '流程状态：1-运行中 2-已完成 3-已取消 4-已挂起 5-异常终止',
    current_activity_id VARCHAR(100) COMMENT '当前活动节点ID',
    current_activity_name VARCHAR(200) COMMENT '当前活动节点名称',
    current_assignee_ids JSON COMMENT '当前处理人ID列表',
    process_variables JSON COMMENT '流程变量快照',
    form_data JSON COMMENT '表单数据快照',
    priority TINYINT DEFAULT 2 COMMENT '优先级：1-高 2-中 3-低',
    urgency_level TINYINT DEFAULT 2 COMMENT '紧急程度：1-紧急 2-普通 3-不急',
    expected_completion_time DATETIME COMMENT '预期完成时间',
    actual_completion_time DATETIME COMMENT '实际完成时间',
    sla_status TINYINT COMMENT 'SLA状态：1-正常 2-预警 3-超时',
    escalation_count INT DEFAULT 0 COMMENT '升级次数',
    last_escalation_time DATETIME COMMENT '最后升级时间',
    suspension_reason VARCHAR(200) COMMENT '挂起原因',
    suspension_time DATETIME COMMENT '挂起时间',
    resume_time DATETIME COMMENT '恢复时间',
    cancellation_reason VARCHAR(200) COMMENT '取消原因',
    cancellation_time DATETIME COMMENT '取消时间',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',

    -- 索引设计
    UNIQUE KEY uk_application_id (application_id),
    UNIQUE KEY uk_process_instance_id (process_instance_id),
    INDEX idx_process_definition_key (process_definition_key),
    INDEX idx_business_key (business_key),
    INDEX idx_start_user_id (start_user_id),
    INDEX idx_process_status (process_status),
    INDEX idx_current_activity_id (current_activity_id),
    INDEX idx_priority (priority),
    INDEX idx_sla_status (sla_status),
    INDEX idx_start_time (start_time),
    INDEX idx_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客流程实例扩展表';
```

#### 2. 与BpmProcessInstanceApiService集成

##### 集成服务实现 (VisitorProcessInstanceService.java)

```java
@Service
@Slf4j
public class VisitorProcessInstanceService {

    @Resource
    private BpmProcessInstanceApiService bpmProcessInstanceApiService;

    @Resource
    private VisitorProcessInstanceExtMapper processInstanceExtMapper;

    @Resource
    private VisitorProcessOperationMapper processOperationMapper;

    @Resource
    private VisitorStatusHistoryMapper statusHistoryMapper;

    /**
     * 启动访客申请流程
     */
    @Transactional(rollbackFor = Exception.class)
    public String startVisitorProcess(VisitorApplicationDO application) {
        // 1. 构建流程启动参数
        BpmProcessInstanceCreateReqDTO createReqDTO = buildProcessCreateRequest(application);

        // 2. 调用BPM服务启动流程
        String processInstanceId = bpmProcessInstanceApiService.createProcessInstanceApi(
            application.getCreatorId(), createReqDTO);

        // 3. 创建流程实例扩展记录
        createProcessInstanceExt(application, processInstanceId);

        // 4. 记录流程操作
        recordProcessOperation(application.getId(), processInstanceId, null,
            VisitorOperationTypeEnum.SUBMIT_APPLICATION, VisitorOperationResultEnum.SUCCESS,
            application.getCreatorId(), "提交访客申请");

        // 5. 记录状态变更
        recordStatusChange(application.getId(), processInstanceId, null,
            VisitorStatusEnum.PENDING_CONFIRM.getStatus(), "流程启动");

        return processInstanceId;
    }

    /**
     * 处理流程完成事件
     */
    @EventListener
    public void handleProcessCompleted(ProcessInstance processInstance) {
        try {
            // 1. 获取业务数据
            VisitorProcessInstanceExtDO processExt = getProcessInstanceExt(processInstance.getId());
            if (processExt == null) {
                return;
            }

            // 2. 更新流程扩展信息
            updateProcessInstanceExt(processExt, processInstance);

            // 3. 记录流程完成操作
            recordProcessOperation(processExt.getApplicationId(), processInstance.getId(), null,
                VisitorOperationTypeEnum.PROCESS_COMPLETED, VisitorOperationResultEnum.SUCCESS,
                null, "流程完成");

            // 4. 处理业务逻辑
            handleBusinessLogicOnProcessCompleted(processExt, processInstance);

        } catch (Exception e) {
            log.error("处理流程完成事件失败", e);
            // 记录异常
            recordProcessException(processInstance.getId(), "流程完成事件处理异常", e);
        }
    }

    /**
     * 构建流程创建请求
     */
    private BpmProcessInstanceCreateReqDTO buildProcessCreateRequest(VisitorApplicationDO application) {
        BpmProcessInstanceCreateReqDTO createReqDTO = new BpmProcessInstanceCreateReqDTO();

        // 根据访客类型选择流程定义
        String processDefinitionKey = getProcessDefinitionKey(application.getVisitorType());
        createReqDTO.setProcessDefinitionKey(processDefinitionKey);

        // 设置业务Key
        createReqDTO.setBusinessKey(application.getApplicationNo());

        // 设置流程变量
        Map<String, Object> variables = buildProcessVariables(application);
        createReqDTO.setVariables(variables);

        return createReqDTO;
    }

    /**
     * 构建流程变量
     */
    private Map<String, Object> buildProcessVariables(VisitorApplicationDO application) {
        Map<String, Object> variables = new HashMap<>();

        // 基础信息
        variables.put("applicationId", application.getId());
        variables.put("applicationNo", application.getApplicationNo());
        variables.put("visitorType", application.getVisitorType());
        variables.put("visitorName", application.getVisitorName());
        variables.put("companyName", application.getCompanyName());
        variables.put("visitReason", application.getVisitReason());

        // 联系人信息
        variables.put("contactPerson", application.getContactPerson());
        variables.put("contactDeptId", application.getContactDeptId());
        variables.put("contactPhone", application.getContactPhone());

        // 访问信息
        variables.put("visitStartTime", application.getVisitStartTime());
        variables.put("visitEndTime", application.getVisitEndTime());
        variables.put("visitArea", application.getVisitArea());

        // 特殊标识
        variables.put("hasVehicle", application.getHasVehicle());
        variables.put("needAccommodation", application.getNeedAccommodation());
        variables.put("companionCount", application.getCompanionCount());

        return variables;
    }

    /**
     * 记录流程操作
     */
    private void recordProcessOperation(Long applicationId, String processInstanceId, String taskId,
                                      VisitorOperationTypeEnum operationType, VisitorOperationResultEnum result,
                                      Long operatorId, String operationContent) {
        VisitorProcessOperationDO operation = new VisitorProcessOperationDO();
        operation.setApplicationId(applicationId);
        operation.setProcessInstanceId(processInstanceId);
        operation.setTaskId(taskId);
        operation.setOperationType(operationType.getType());
        operation.setOperationResult(result.getResult());
        operation.setOperatorId(operatorId);
        operation.setOperationTime(LocalDateTime.now());
        operation.setOperationContent(operationContent);

        processOperationMapper.insert(operation);
    }

    /**
     * 记录状态变更
     */
    private void recordStatusChange(Long applicationId, String processInstanceId, Integer beforeStatus,
                                  Integer afterStatus, String changeReason) {
        VisitorStatusHistoryDO statusHistory = new VisitorStatusHistoryDO();
        statusHistory.setApplicationId(applicationId);
        statusHistory.setProcessInstanceId(processInstanceId);
        statusHistory.setBeforeStatus(beforeStatus);
        statusHistory.setAfterStatus(afterStatus);
        statusHistory.setChangeReason(changeReason);
        statusHistory.setChangeTime(LocalDateTime.now());
        statusHistory.setChangeType(VisitorStatusChangeTypeEnum.NORMAL_FLOW.getType());

        // 计算序号
        Integer maxSequence = statusHistoryMapper.getMaxSequenceByApplicationId(applicationId);
        statusHistory.setChangeSequence(maxSequence == null ? 1 : maxSequence + 1);

        statusHistoryMapper.insert(statusHistory);
    }
}
```

### 数据库初始化脚本

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS yudao_visitor DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE yudao_visitor;

-- 插入默认培训配置
INSERT INTO visitor_training (training_name, training_type, training_content, min_duration, visitor_type, is_required, status) VALUES
('园区安全培训', 1, '园区安全规章制度、应急预案、安全注意事项', 10, 0, 1, 1),
('施工安全培训', 1, '施工现场安全规范、防护用品使用、危险源识别', 30, 3, 1, 1),
('访客行为规范', 3, '访客行为准则、保密要求、参观路线', 5, 1, 1, 1);

-- 插入字典数据
INSERT INTO system_dict_type (name, type, status, remark) VALUES
('访客类型', 'visitor_type', 0, '访客类型'),
('访客状态', 'visitor_status', 0, '访客状态'),
('培训类型', 'visitor_training_type', 0, '访客培训类型'),
('审批结果', 'approval_result', 0, '审批结果');

INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '普通访客', '1', 'visitor_type', 0),
(2, '政府访客', '2', 'visitor_type', 0),
(3, '施工承包商', '3', 'visitor_type', 0),
(1, '待确认', '1', 'visitor_status', 0),
(2, '已审批', '2', 'visitor_status', 0),
(3, '已驳回', '3', 'visitor_status', 0),
(4, '已入园', '4', 'visitor_status', 0),
(5, '已出园', '5', 'visitor_status', 0);

-- 插入流程操作类型字典
INSERT INTO system_dict_type (name, type, status, remark) VALUES
('访客流程操作类型', 'visitor_operation_type', 0, '访客流程操作类型');

INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '提交申请', '1', 'visitor_operation_type', 0),
(2, '联系人确认', '2', 'visitor_operation_type', 0),
(3, '部门审批', '3', 'visitor_operation_type', 0),
(4, '综管部审批', '4', 'visitor_operation_type', 0),
(5, '安全部审批', '5', 'visitor_operation_type', 0),
(6, '生成二维码', '6', 'visitor_operation_type', 0),
(7, '入园登记', '7', 'visitor_operation_type', 0),
(8, '出园登记', '8', 'visitor_operation_type', 0),
(9, '流程取消', '9', 'visitor_operation_type', 0),
(10, '流程驳回', '10', 'visitor_operation_type', 0);

-- 插入流程异常类型字典
INSERT INTO system_dict_type (name, type, status, remark) VALUES
('访客流程异常类型', 'visitor_exception_type', 0, '访客流程异常类型');

INSERT INTO system_dict_data (sort, label, value, dict_type, status) VALUES
(1, '超时未处理', '1', 'visitor_exception_type', 0),
(2, '系统错误', '2', 'visitor_exception_type', 0),
(3, '数据异常', '3', 'visitor_exception_type', 0),
(4, '权限异常', '4', 'visitor_exception_type', 0),
(5, '业务规则异常', '5', 'visitor_exception_type', 0),
(6, '外部服务异常', '6', 'visitor_exception_type', 0),
(7, '人工干预', '7', 'visitor_exception_type', 0),
(8, '流程中断', '8', 'visitor_exception_type', 0);
```

### 流程记录表关联关系说明

#### 1. 表关联关系图

##### A. 核心业务表关联关系图

```mermaid
graph TB
    subgraph "访客申请核心"
        VA[visitor_application<br/>访客申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>visitor_name: 访客姓名<br/>contact_person: 联系人<br/>status: 状态]

        VPIE[visitor_process_instance_ext<br/>流程实例扩展表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>process_instance_id: 流程实例ID<br/>business_key: 业务Key<br/>process_status: 流程状态]
    end

    subgraph "流程记录数据"
        VPO[visitor_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>operation_type: 操作类型<br/>operator_id: 操作人<br/>operation_time: 操作时间]

        VAO[visitor_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>approval_level: 审批级别<br/>approval_result: 审批结果<br/>opinion_content: 审批意见]

        VSH[visitor_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态<br/>change_time: 变更时间]

        VPF[visitor_process_follow<br/>流程跟进记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>follow_type: 跟进类型<br/>follow_content: 跟进内容<br/>urgency_level: 紧急程度]

        VPE[visitor_process_exception<br/>流程异常处理记录表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_id: 申请ID<br/>exception_type: 异常类型<br/>handling_status: 处理状态<br/>escalation_level: 升级级别]
    end

    %% 关联关系
    VA -->|1:1<br/>id = application_id| VPIE
    VA -->|1:N<br/>id = application_id| VPO
    VA -->|1:N<br/>id = application_id| VAO
    VA -->|1:N<br/>id = application_id| VSH
    VA -->|1:N<br/>id = application_id| VPF
    VA -->|1:N<br/>id = application_id| VPE

    VPO -.->|1:1<br/>关联操作| VSH

    style VA fill:#e3f2fd
    style VPIE fill:#e3f2fd
    style VPO fill:#f3e5f5
    style VAO fill:#f3e5f5
    style VSH fill:#f3e5f5
    style VPF fill:#f3e5f5
    style VPE fill:#f3e5f5
```

#### 2. 业务表与Flowable工作流表关系图

##### A. 流程实例级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        VA[visitor_application<br/>访客申请表<br/>━━━━━━━━━━━━━<br/>id: 主键<br/>application_no: 申请单号<br/>process_instance_id: 流程实例ID<br/>status: 申请状态]

        VPE[visitor_process_instance_ext<br/>流程实例扩展表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>process_instance_id: 流程实例ID<br/>business_key: 业务Key<br/>process_status: 流程状态]
    end

    subgraph "Flowable工作流层"
        ARE[ACT_RU_EXECUTION<br/>运行时流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>ACT_ID_: 当前活动ID<br/>START_USER_ID_: 发起人]

        AHP[ACT_HI_PROCINST<br/>历史流程实例表<br/>━━━━━━━━━━━━━<br/>PROC_INST_ID_: 流程实例ID<br/>BUSINESS_KEY_: 业务Key<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    VA -.->|1:1<br/>process_instance_id| ARE
    VPE -.->|1:1<br/>process_instance_id| AHP
    VA -->|1:1<br/>id = application_id| VPE

    style VA fill:#e1f5fe
    style VPE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

##### B. 任务级别关联关系图

```mermaid
graph TB
    subgraph "业务层"
        VAO[visitor_approval_opinion<br/>审批意见表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>approver_id: 审批人<br/>approval_result: 审批结果]

        VPO[visitor_process_operation<br/>流程操作记录表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>task_id: 任务ID<br/>operation_type: 操作类型<br/>operator_id: 操作人]

        VSH[visitor_status_history<br/>状态变更历史表<br/>━━━━━━━━━━━━━<br/>application_id: 申请ID<br/>related_task_id: 关联任务ID<br/>before_status: 变更前状态<br/>after_status: 变更后状态]
    end

    subgraph "Flowable工作流层"
        ART[ACT_RU_TASK<br/>运行时任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>TASK_DEF_KEY_: 任务定义Key<br/>ASSIGNEE_: 处理人]

        AHT[ACT_HI_TASKINST<br/>历史任务表<br/>━━━━━━━━━━━━━<br/>ID_: 任务ID<br/>PROC_INST_ID_: 流程实例ID<br/>START_TIME_: 开始时间<br/>END_TIME_: 结束时间]
    end

    VAO -.->|N:1<br/>task_id = ID_| ART
    VPO -.->|N:1<br/>task_id = ID_| AHT
    VSH -.->|N:1<br/>related_task_id = ID_| AHT

    style VAO fill:#e8f5e8
    style VPO fill:#e8f5e8
    style VSH fill:#e8f5e8
    style ART fill:#fff3e0
    style AHT fill:#fff3e0
```

##### C. 业务Key关联关系图

```mermaid
graph LR
    subgraph "业务标识"
        AN[application_no<br/>申请单号<br/>格式: VA20250111001]
    end

    subgraph "业务表"
        VA[visitor_application<br/>application_no]
        VPE[visitor_process_instance_ext<br/>business_key]
    end

    subgraph "Flowable表"
        ARE[ACT_RU_EXECUTION<br/>BUSINESS_KEY_]
        AHP[ACT_HI_PROCINST<br/>BUSINESS_KEY_]
    end

    AN --> VA
    AN --> VPE
    AN --> ARE
    AN --> AHP

    style AN fill:#ffebee
    style VA fill:#e1f5fe
    style VPE fill:#e1f5fe
    style ARE fill:#fff3e0
    style AHP fill:#fff3e0
```

##### D. 数据流向和同步时序图

```mermaid
sequenceDiagram
    participant User as 用户/系统
    participant BizDB as 业务数据库
    participant Flowable as Flowable引擎
    participant Event as 事件监听器

    Note over User,Event: 1. 流程启动阶段
    User->>BizDB: 1.创建visitor_application记录
    User->>Flowable: 2.启动流程实例
    Flowable->>Flowable: 3.创建ACT_RU_EXECUTION记录
    Flowable->>Flowable: 4.创建ACT_HI_PROCINST记录
    Flowable-->>BizDB: 5.返回process_instance_id
    BizDB->>BizDB: 6.更新visitor_application.process_instance_id
    BizDB->>BizDB: 7.创建visitor_process_instance_ext记录
    BizDB->>BizDB: 8.创建visitor_process_operation记录

    Note over User,Event: 2. 任务处理阶段
    Flowable->>Flowable: 1.创建ACT_RU_TASK记录
    Event->>BizDB: 2.记录visitor_process_operation(任务创建)
    User->>BizDB: 3.创建visitor_approval_opinion记录
    User->>Flowable: 4.完成任务
    Flowable->>Flowable: 5.移动任务到ACT_HI_TASKINST
    Event->>BizDB: 6.记录visitor_process_operation(任务完成)
    Event->>BizDB: 7.记录visitor_status_history

    Note over User,Event: 3. 流程完成阶段
    Flowable->>Flowable: 1.更新ACT_HI_PROCINST.END_TIME_
    Flowable->>Flowable: 2.删除ACT_RU_EXECUTION记录
    Event->>BizDB: 3.更新visitor_process_instance_ext状态
    Event->>BizDB: 4.更新visitor_application.status
    Event->>BizDB: 5.记录visitor_process_operation(流程完成)
```

**E. 关联关系汇总表**

| 序号 | 业务表 | Flowable表 | 关联字段 | 关系 | 同步时机 | 用途 |
|------|--------|------------|----------|------|----------|------|
| 1 | visitor_application | ACT_RU_EXECUTION | process_instance_id = PROC_INST_ID_ | 1:1 | 流程启动时 | 查询当前流程状态 |
| 2 | visitor_process_instance_ext | ACT_HI_PROCINST | process_instance_id = PROC_INST_ID_ | 1:1 | 流程启动时 | 流程历史分析 |
| 3 | visitor_approval_opinion | ACT_RU_TASK | task_id = ID_ | N:1 | 任务完成时 | 记录审批意见 |
| 4 | visitor_process_operation | ACT_HI_TASKINST | task_id = ID_ | N:1 | 任务状态变化时 | 操作审计追踪 |
| 5 | visitor_status_history | ACT_HI_TASKINST | related_task_id = ID_ | N:1 | 状态变更时 | 状态变更历史 |
| 6 | visitor_application | ACT_RU_EXECUTION | application_no = BUSINESS_KEY_ | 1:1 | 流程启动时 | 业务Key查询 |

#### 3. 关联关系详细说明

##### A. 流程实例级别关联

###### 1. visitor_application 与 ACT_RU_EXECUTION 的关联关系

这是业务申请表与Flowable运行时流程实例表之间的核心关联关系。当访客提交申请并启动审批流程时，系统会在Flowable引擎中创建一个流程实例，该实例的ID会存储在`visitor_application.process_instance_id`字段中。

- **关联字段映射**：`visitor_application.process_instance_id` = `ACT_RU_EXECUTION.PROC_INST_ID_`
- **关系类型**：1:1（一个访客申请对应一个流程实例）
- **业务含义**：通过此关联可以查询访客申请当前的流程状态、当前处理节点、处理人等信息
- **数据流向**：业务操作触发流程引擎，流程状态变化反映到业务数据
- **查询场景**：查询某个申请的当前审批状态、查询某个流程实例对应的业务数据

###### 2. visitor_process_instance_ext 与 ACT_HI_PROCINST 的关联关系

这是业务流程扩展表与Flowable历史流程实例表之间的映射关系。扩展表存储了业务相关的流程信息，与Flowable的历史表形成互补。

- **关联字段映射**：`visitor_process_instance_ext.process_instance_id` = `ACT_HI_PROCINST.PROC_INST_ID_`
- **关系类型**：1:1（一个业务流程扩展记录对应一个历史流程实例）
- **业务含义**：扩展表补充了Flowable历史表中缺少的业务信息，如SLA状态、升级次数、业务优先级等
- **数据同步**：流程启动时同时创建两个表的记录，流程状态变化时同步更新
- **查询场景**：流程历史分析、SLA监控、流程效率统计

**数据同步时机和机制：**
- **流程启动时**：
  - 调用`RuntimeService.startProcessInstanceByKey()`启动流程
  - Flowable自动创建`ACT_RU_EXECUTION`和`ACT_HI_PROCINST`记录
  - 业务系统创建`visitor_process_instance_ext`记录，存储流程ID和业务扩展信息
  - 更新`visitor_application.process_instance_id`字段建立关联
- **流程状态变更时**：
  - Flowable引擎更新`ACT_RU_EXECUTION`的状态信息
  - 通过事件监听器同步更新`visitor_process_instance_ext.process_status`
  - 业务系统根据流程状态更新`visitor_application.status`
- **流程完成时**：
  - Flowable将`ACT_RU_EXECUTION`记录移动到历史表
  - 更新`ACT_HI_PROCINST.END_TIME_`和相关状态
  - 同步更新`visitor_process_instance_ext`的结束时间和最终状态

**B. 任务级别关联**

**1. visitor_approval_opinion 与 ACT_RU_TASK 的关联关系**

这是审批意见表与Flowable运行时任务表之间的关联关系。每当流程流转到需要人工审批的节点时，Flowable会创建一个任务，审批人完成审批后，系统会在审批意见表中记录详细的审批信息。

- **关联字段映射**：`visitor_approval_opinion.task_id` = `ACT_RU_TASK.ID_`
- **关系类型**：N:1（一个任务可能有多条审批意见，如会签场景）
- **业务含义**：记录每个审批任务的详细审批意见、附件、风险评估等业务信息
- **数据流向**：任务创建时获取任务信息，任务完成时记录审批结果
- **查询场景**：查询某个任务的审批意见、统计审批人的审批历史

**2. visitor_process_operation 与 ACT_HI_TASKINST 的关联关系**

这是流程操作记录表与Flowable历史任务表之间的关联关系。系统会记录每个任务相关的所有操作，包括任务分配、处理、完成等操作。

- **关联字段映射**：`visitor_process_operation.task_id` = `ACT_HI_TASKINST.ID_`
- **关系类型**：N:1（一个任务可能有多个操作记录，如分配、处理、完成）
- **业务含义**：记录任务生命周期中的所有操作，包括系统自动操作和人工操作
- **数据流向**：任务状态变化时实时记录操作信息
- **查询场景**：审计追踪、操作历史查询、性能分析

**3. visitor_status_history 与 ACT_HI_TASKINST 的关联关系**

这是状态变更历史表与Flowable历史任务表之间的关联关系。业务状态的变更通常与任务的完成相关联。

- **关联字段映射**：`visitor_status_history.related_task_id` = `ACT_HI_TASKINST.ID_`
- **关系类型**：N:1（一个任务完成可能触发多个状态变更）
- **业务含义**：记录因任务完成而引起的业务状态变更
- **数据流向**：任务完成时触发状态变更记录
- **查询场景**：状态变更历史追踪、流程节点分析

**任务级别数据同步时机和机制：**
- **任务创建时**：
  - Flowable引擎创建`ACT_RU_TASK`记录
  - 系统在`visitor_process_operation`中记录任务分配操作
  - 记录操作类型为"任务分配"，包含任务信息和分配人信息
- **任务处理过程中**：
  - 审批人查看任务时，记录"查看任务"操作
  - 审批人保存草稿时，记录"保存草稿"操作
  - 任务转交时，记录"任务转交"操作
- **任务完成时**：
  - 审批人提交审批意见，在`visitor_approval_opinion`中记录详细审批信息
  - 调用`TaskService.complete()`完成任务
  - Flowable将任务从`ACT_RU_TASK`移动到`ACT_HI_TASKINST`
  - 在`visitor_process_operation`中记录任务完成操作
  - 根据审批结果在`visitor_status_history`中记录状态变更

**C. 业务Key关联**

**业务Key的作用和映射关系**

业务Key是连接业务数据和工作流数据的重要桥梁，它提供了一种通过业务标识快速定位流程实例的方式。

**1. visitor_application.application_no → ACT_RU_EXECUTION.BUSINESS_KEY_**

- **映射关系**：访客申请单号直接作为Flowable流程实例的业务Key
- **业务含义**：通过申请单号可以快速定位到对应的流程实例
- **数据同步**：流程启动时将申请单号设置为业务Key
- **查询优势**：支持通过申请单号快速查询流程状态，无需知道流程实例ID
- **使用场景**：
  - 客服人员通过申请单号查询审批进度
  - 系统集成时通过业务单号进行数据交换
  - 异常处理时通过申请单号定位问题流程

**2. visitor_process_instance_ext.business_key → ACT_HI_PROCINST.BUSINESS_KEY_**

- **映射关系**：业务流程扩展表中保存业务Key的副本，与历史流程实例表保持一致
- **业务含义**：确保业务Key在流程完成后仍然可以查询
- **数据一致性**：两个表中的业务Key必须保持一致
- **查询优势**：支持历史流程的业务Key查询
- **使用场景**：
  - 历史数据分析和统计
  - 流程完成后的数据追溯
  - 业务数据与历史流程数据的关联查询

**业务Key的唯一性保证**

- **格式规范**：申请单号格式为"VA" + 日期(YYYYMMDD) + 访客类型 + 4位序号
- **唯一性约束**：申请单号在系统中全局唯一
- **冲突处理**：如果业务Key重复，流程启动会失败，需要重新生成申请单号
- **索引优化**：在业务Key字段上建立索引，提高查询性能

#### 4. 数据一致性保证机制

**A. 事务控制策略**

```java
@Transactional(rollbackFor = Exception.class)
public void processVisitorApproval(VisitorApprovalReqVO reqVO) {
    // 1. 更新业务数据
    updateVisitorApplication(reqVO);

    // 2. 记录审批意见
    saveApprovalOpinion(reqVO);

    // 3. 记录操作历史
    saveProcessOperation(reqVO);

    // 4. 完成Flowable任务
    taskService.complete(reqVO.getTaskId(), variables);

    // 5. 记录状态变更
    saveStatusHistory(reqVO);
}
```

**B. 事件监听同步**

```java
@EventListener
public void handleTaskCompleted(TaskCompletedEvent event) {
    // 同步任务完成状态到业务表
    syncTaskStatusToBusiness(event.getTaskId(), event.getProcessInstanceId());
}

@EventListener
public void handleProcessCompleted(ProcessCompletedEvent event) {
    // 同步流程完成状态到业务表
    syncProcessStatusToBusiness(event.getProcessInstanceId());
}
```

**C. 数据校验和修复**

```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void validateDataConsistency() {
    // 1. 检查业务表与Flowable表的数据一致性
    List<String> inconsistentProcesses = findInconsistentProcesses();

    // 2. 修复不一致的数据
    for (String processInstanceId : inconsistentProcesses) {
        repairDataInconsistency(processInstanceId);
    }

    // 3. 记录修复日志
    logRepairResults(inconsistentProcesses);
}
```

#### 5. 查询和分析应用

**A. 跨表查询示例**

```sql
-- 查询审批中的申请及其当前任务信息
SELECT
    va.application_no,
    va.visitor_name,
    va.status,
    rt.NAME_ as current_task_name,
    rt.ASSIGNEE_ as current_assignee,
    rt.CREATE_TIME_ as task_create_time
FROM visitor_application va
JOIN ACT_RU_EXECUTION re ON va.process_instance_id = re.PROC_INST_ID_
JOIN ACT_RU_TASK rt ON re.PROC_INST_ID_ = rt.PROC_INST_ID_
WHERE va.status IN (1, 2); -- 待确认、已审批状态

-- 查询已完成流程的审批历史
SELECT
    va.application_no,
    va.visitor_name,
    vao.approval_level,
    vao.approver_name,
    vao.approval_result,
    vao.opinion_content,
    hti.START_TIME_ as task_start_time,
    hti.END_TIME_ as task_end_time
FROM visitor_application va
JOIN visitor_approval_opinion vao ON va.id = vao.application_id
JOIN ACT_HI_TASKINST hti ON vao.task_id = hti.ID_
WHERE va.status = 5 -- 已出园
ORDER BY va.id, vao.approval_level;
```

**B. 流程效率分析**

```sql
-- 分析各审批环节的平均处理时长
SELECT
    hti.TASK_DEF_KEY_ as task_key,
    hti.NAME_ as task_name,
    COUNT(*) as task_count,
    AVG(TIMESTAMPDIFF(MINUTE, hti.START_TIME_, hti.END_TIME_)) as avg_duration_minutes,
    MAX(TIMESTAMPDIFF(MINUTE, hti.START_TIME_, hti.END_TIME_)) as max_duration_minutes
FROM ACT_HI_TASKINST hti
JOIN visitor_process_operation vpo ON hti.ID_ = vpo.task_id
WHERE hti.END_TIME_ IS NOT NULL
  AND hti.START_TIME_ >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY hti.TASK_DEF_KEY_, hti.NAME_
ORDER BY avg_duration_minutes DESC;
```

#### 6. 业务流程中的记录表作用

**在申请提交阶段：**
- `visitor_process_operation`：记录申请提交操作，包含表单数据快照
- `visitor_status_history`：记录状态从无到"待确认"的变更
- `visitor_process_instance_ext`：创建流程实例扩展记录，关联Flowable流程

**在联系人确认阶段：**
- `visitor_process_operation`：记录联系人确认操作和结果
- `visitor_process_follow`：记录与联系人的沟通过程
- `visitor_status_history`：记录状态变更（确认通过/驳回）
- `visitor_process_exception`：记录超时未确认等异常情况

**在部门审批阶段：**
- `visitor_approval_opinion`：详细记录每级审批的意见和附件
- `visitor_process_operation`：记录审批操作的基本信息
- `visitor_process_follow`：记录审批过程中的沟通跟进
- `visitor_status_history`：记录审批状态的变更历史

**在二维码生成阶段：**
- `visitor_process_operation`：记录二维码生成操作
- `visitor_process_exception`：记录生成失败等异常情况

**在入园出园阶段：**
- `visitor_process_operation`：记录入园出园操作
- `visitor_process_follow`：记录现场沟通情况
- `visitor_process_exception`：记录验证失败等异常情况

#### 3. 数据一致性保证

**应用层数据一致性控制：**
- 所有表均不使用数据库外键约束，通过应用层逻辑维护数据一致性
- 使用`@Transactional`注解确保业务操作和相关记录在同一事务中执行
- 异常情况下自动回滚所有相关记录，避免数据不一致

**数据完整性验证：**
- 在Service层进行关联数据的存在性验证
- 删除主记录前检查是否存在关联的子记录
- 提供数据完整性检查工具，定期验证数据关联关系

**数据同步机制：**
- 通过Flowable事件监听器同步流程状态到业务表
- 使用定时任务检查业务数据与流程数据的一致性
- 提供数据修复工具处理发现的不一致情况

**级联操作处理：**
- 在应用层实现逻辑删除，避免物理删除导致的数据丢失
- 删除访客申请时，同步更新所有关联记录的状态
- 提供批量数据清理功能，定期清理过期的无效数据

**审计追踪：**
- 所有数据变更操作都有完整的审计记录
- 支持按时间、操作人、操作类型等维度查询
- 提供流程回溯和问题定位功能
- 记录数据一致性检查和修复的操作日志

## API接口设计

### 管理端接口

#### 访客申请管理接口

**VisitorApplicationController.java**

```java
@RestController
@RequestMapping("/admin-api/visitor/application")
@Tag(name = "管理后台 - 访客申请管理")
@Validated
public class VisitorApplicationController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @GetMapping("/page")
    @Operation(summary = "获得访客申请分页")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<PageResult<VisitorApplicationRespVO>> getApplicationPage(
            @Valid VisitorApplicationPageReqVO pageReqVO) {
        PageResult<VisitorApplicationDO> pageResult =
            visitorApplicationService.getApplicationPage(pageReqVO);
        return success(VisitorApplicationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客申请详情")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<VisitorApplicationRespVO> getApplication(@RequestParam("id") Long id) {
        VisitorApplicationDO application = visitorApplicationService.getApplication(id);
        return success(VisitorApplicationConvert.INSTANCE.convert(application));
    }

    @PostMapping("/create")
    @Operation(summary = "创建访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:create')")
    public CommonResult<Long> createApplication(@Valid @RequestBody VisitorApplicationSaveReqVO createReqVO) {
        return success(visitorApplicationService.createApplication(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:update')")
    public CommonResult<Boolean> updateApplication(@Valid @RequestBody VisitorApplicationSaveReqVO updateReqVO) {
        visitorApplicationService.updateApplication(updateReqVO);
        return success(true);
    }

    @PostMapping("/approve")
    @Operation(summary = "审批访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:approve')")
    public CommonResult<Boolean> approveApplication(@Valid @RequestBody VisitorApprovalReqVO reqVO) {
        visitorApplicationService.approveApplication(reqVO);
        return success(true);
    }

    @PostMapping("/check-in")
    @Operation(summary = "访客入园签到")
    @PreAuthorize("@ss.hasPermission('visitor:application:checkin')")
    public CommonResult<Boolean> checkIn(@Valid @RequestBody VisitorCheckInReqVO reqVO) {
        visitorApplicationService.checkIn(reqVO);
        return success(true);
    }

    @PostMapping("/check-out")
    @Operation(summary = "访客出园签到")
    @PreAuthorize("@ss.hasPermission('visitor:application:checkout')")
    public CommonResult<Boolean> checkOut(@Valid @RequestBody VisitorCheckOutReqVO reqVO) {
        visitorApplicationService.checkOut(reqVO);
        return success(true);
    }

    @GetMapping("/qrcode")
    @Operation(summary = "获取访客二维码")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<String> getQrCode(@RequestParam("id") Long id) {
        String qrCodeUrl = visitorApplicationService.generateQrCode(id);
        return success(qrCodeUrl);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出访客申请Excel")
    @PreAuthorize("@ss.hasPermission('visitor:application:export')")
    public void exportApplicationExcel(@Valid VisitorApplicationPageReqVO pageReqVO,
                                     HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VisitorApplicationDO> list = visitorApplicationService.getApplicationPage(pageReqVO).getList();
        ExcelUtils.write(response, "访客申请.xls", "数据", VisitorApplicationRespVO.class,
                VisitorApplicationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取访客统计数据")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<VisitorStatisticsRespVO> getStatistics(
            @Valid VisitorStatisticsReqVO reqVO) {
        VisitorStatisticsRespVO statistics = visitorApplicationService.getStatistics(reqVO);
        return success(statistics);
    }
}
```

### 移动端接口

#### AppVisitorApplicationController.java

```java
@RestController
@RequestMapping("/app-api/visitor/application")
@Tag(name = "用户APP - 访客申请")
@Validated
public class AppVisitorApplicationController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @PostMapping("/create")
    @Operation(summary = "创建访客申请")
    public CommonResult<Long> createApplication(@Valid @RequestBody AppVisitorCreateReqVO createReqVO) {
        return success(visitorApplicationService.createApplicationByApp(createReqVO));
    }

    @GetMapping("/my")
    @Operation(summary = "获取我的访客申请")
    public CommonResult<List<AppVisitorApplicationRespVO>> getMyApplications() {
        Long userId = getLoginUserId();
        List<VisitorApplicationDO> applications =
            visitorApplicationService.getApplicationsByCreator(userId);
        return success(AppVisitorApplicationConvert.INSTANCE.convertList(applications));
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客申请详情")
    public CommonResult<AppVisitorApplicationRespVO> getApplication(@RequestParam("id") Long id) {
        VisitorApplicationDO application = visitorApplicationService.getApplication(id);
        return success(AppVisitorApplicationConvert.INSTANCE.convert(application));
    }

    @GetMapping("/qrcode")
    @Operation(summary = "获取通行二维码")
    public CommonResult<String> getQrCode(@RequestParam("applicationId") Long applicationId) {
        String qrCodeUrl = visitorApplicationService.getQrCode(applicationId);
        return success(qrCodeUrl);
    }

    @PostMapping("/training/complete")
    @Operation(summary = "完成安全培训")
    public CommonResult<Boolean> completeTraining(@Valid @RequestBody AppTrainingCompleteReqVO reqVO) {
        visitorApplicationService.completeTraining(reqVO);
        return success(true);
    }
}
```

#### AppGuardController.java

```java
@RestController
@RequestMapping("/app-api/visitor/guard")
@Tag(name = "用户APP - 警卫操作")
@Validated
public class AppGuardController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorRecordService visitorRecordService;

    @PostMapping("/scan")
    @Operation(summary = "扫描访客二维码")
    public CommonResult<AppQrCodeScanRespVO> scanVisitorQrCode(@Valid @RequestBody AppQrCodeScanReqVO reqVO) {
        AppQrCodeScanRespVO result = visitorApplicationService.scanQrCode(reqVO.getQrCodeContent());
        return success(result);
    }

    @PostMapping("/check-in")
    @Operation(summary = "访客入园登记")
    public CommonResult<Boolean> visitorCheckIn(@Valid @RequestBody AppVisitorCheckInReqVO reqVO) {
        visitorRecordService.checkIn(reqVO);
        return success(true);
    }

    @PostMapping("/check-out")
    @Operation(summary = "访客出园登记")
    public CommonResult<Boolean> visitorCheckOut(@Valid @RequestBody AppVisitorCheckOutReqVO reqVO) {
        visitorRecordService.checkOut(reqVO);
        return success(true);
    }
}
```

## 业务流程设计

### 1. 访客申请审批流程图

```mermaid
graph TD
    A[访客提交申请] --> B[系统自动校验]
    B --> C{校验是否通过}
    C -->|否| D[返回错误信息]
    C -->|是| E[发送通知给联系人]
    E --> F[联系人确认]
    F --> G{联系人是否确认}
    G -->|否| H[申请被驳回]
    G -->|是| I{访客类型判断}

    I -->|普通访客| J[部门主管审批]
    I -->|政府访客| K[综管部直接审批]
    I -->|施工承包商| L[安全部门审批]

    J --> M{部门审批结果}
    K --> N{综管部审批结果}
    L --> O{安全部门审批结果}

    M -->|通过| P[综管部终审]
    M -->|驳回| H
    N -->|通过| Q[生成通行二维码]
    N -->|驳回| H
    O -->|通过| P
    O -->|驳回| H

    P --> R{终审结果}
    R -->|通过| Q
    R -->|驳回| H

    Q --> S[发送二维码给访客]
    S --> T[抄送门岗警卫]
    T --> U[访客到达门岗]
    U --> V[扫码验证身份]
    V --> W{验证是否通过}
    W -->|否| X[拒绝入园]
    W -->|是| Y[完成安全培训]
    Y --> Z{培训是否完成}
    Z -->|否| AA[要求完成培训]
    Z -->|是| BB[访客入园]
    BB --> CC[访客活动]
    CC --> DD[访客离园]
    DD --> EE[门岗登记出园]
    EE --> FF[流程结束]

    H --> GG[发送驳回通知]
    X --> HH[记录异常情况]
    AA --> Y
```

### 2. 访客状态转换图

```mermaid
stateDiagram-v2
    [*] --> 待确认: 提交申请
    待确认 --> 已审批: 审批通过
    待确认 --> 已驳回: 审批驳回
    已审批 --> 已入园: 扫码入园
    已入园 --> 已出园: 登记出园
    已驳回 --> [*]: 流程结束
    已出园 --> [*]: 流程结束

    待确认 --> 待确认: 修改申请
    已审批 --> 已驳回: 撤销审批
```

### 3. 详细业务流程描述

#### 1. 申请提交阶段

**触发条件：**
- 访客需要进入园区进行商务洽谈、参观考察、施工作业等活动
- 厂内联系人代为申请或访客通过H5页面自主申请
- 访客信息已通过初步安全审查

**前置条件：**
- 访客具备有效身份证件（身份证、护照等）
- 明确的来访目的和厂内联系人信息
- 符合园区基本准入条件（无安全风险记录）
- 访问时间在园区开放时间范围内

**详细操作步骤：**

**步骤1：访问申请页面**
- 访客通过H5链接或联系人通过PC管理端进入申请页面
- 系统显示访客申请表单，包含必填和选填字段
- 根据访客类型（普通访客/政府访客/施工承包商）显示不同的表单字段

**步骤2：填写基本信息**
- **个人信息**：
  - 访客姓名（必填，2-50个字符）
  - 联系电话（必填，11位手机号码）
  - 身份证号（施工承包商必填，18位身份证号）
  - 来访单位（必填，2-100个字符）
  - 职务/职位（选填）
- **访问信息**：
  - 来访事由（必填，详细描述来访目的，10-200字符）
  - 厂内联系人（必填，从系统用户中选择或手动输入）
  - 联系人电话（必填，用于确认和沟通）
  - 联系人部门（自动获取或手动选择）
  - 访问开始时间（必填，不能早于当前时间）
  - 访问结束时间（必填，不能晚于开始时间+7天）
  - 到访厂区（必填，从预设区域中选择）

**步骤3：填写车辆信息（如适用）**
- 是否驾车到访（选择是/否）
- 如选择"是"，需填写：
  - 车牌号码（必填，支持各省市车牌格式）
  - 车辆类型（普通客车/施工车辆）
  - 行驶证编号（施工车辆必填）
  - 驾驶员信息（如非申请人本人）

**步骤4：填写同行人员信息（如适用）**
- 同行人数量（0-10人）
- 每位同行人员信息：
  - 姓名（必填）
  - 联系电话（选填）
  - 身份证号（施工承包商的同行人员必填）
  - 职务（选填）

**步骤5：上传相关资料**
- **必须上传**：
  - 访客本人身份证照片（正反面）
  - 访客本人近期免冠照片
- **条件上传**：
  - 车辆行驶证照片（如驾车）
  - 施工资质证明（施工承包商）
  - 政府部门介绍信（政府访客）
  - 同行人员身份证照片（如有同行人员）

**步骤6：确认特殊需求**
- 是否需要住宿安排（选择是/否）
- 是否需要饭堂就餐（选择是/否）
- 其他特殊要求（文本输入，最多500字符）

**系统自动校验规则：**
- **格式校验**：
  - 手机号码格式：11位数字，1开头
  - 身份证号格式：18位，符合身份证校验规则
  - 车牌号格式：符合国标车牌号规则
  - 时间格式：YYYY-MM-DD HH:mm格式
- **业务规则校验**：
  - 访问开始时间 ≥ 当前时间 + 30分钟
  - 访问结束时间 ≤ 访问开始时间 + 7天
  - 施工承包商必须填写身份证号
  - 同行人数不超过10人
  - 单次访问时长不超过168小时（7天）
- **安全校验**：
  - 访客黑名单检查
  - 车辆黑名单检查
  - 敏感时间段访问限制检查

**数据验证和处理：**
- 实时表单验证，错误字段高亮显示
- 身份证号自动解析性别和年龄信息
- 车牌号自动识别归属地信息
- 联系人信息自动补全部门和职位
- 访问时间冲突检查（同一联系人同时段申请数量限制）

**异常情况处理：**
- **信息不完整**：
  - 实时提示缺失的必填字段
  - 保存草稿功能，允许稍后继续填写
  - 提供字段填写说明和示例
- **格式错误**：
  - 实时格式验证，错误提示
  - 提供正确格式示例
  - 支持格式自动纠正（如电话号码）
- **黑名单访客**：
  - 自动拒绝申请并显示拒绝原因
  - 记录尝试申请日志
  - 通知安保部门异常访问尝试
- **系统异常**：
  - 自动保存已填写内容为草稿
  - 提供重新提交功能
  - 显示友好的错误提示信息
  - 记录系统异常日志供技术人员排查

**成功提交后的系统处理：**
- 生成唯一申请单号（格式：VA + 日期 + 访客类型 + 序号）
- **记录流程操作**：在`visitor_process_operation`表中记录申请提交操作
  - 操作类型：提交申请
  - 操作人：申请人
  - 表单数据快照：完整的申请信息JSON
  - 操作时间和IP地址等审计信息
- **记录状态变更**：在`visitor_status_history`表中记录状态变更
  - 变更前状态：无
  - 变更后状态：待确认
  - 变更原因：申请提交
  - 变更序号：1
- **启动工作流程**：调用Flowable引擎启动审批流程
  - 创建流程实例扩展记录（`visitor_process_instance_ext`）
  - 设置流程变量和业务数据关联
  - 生成第一个审批任务（联系人确认）
- 自动发送企业微信通知给联系人
- 发送短信通知给访客（包含申请单号）
- 更新申请状态为"待确认"
- 设置联系人确认超时提醒（24小时）

#### 2. 联系人确认阶段

**触发条件：**
- 访客申请提交成功并通过系统初步校验
- 系统自动向联系人发送确认通知
- 联系人收到企业微信或短信通知

**角色权限：**
- **主要确认人**：申请中指定的厂内联系人
- **代理确认人**：联系人的直属上级或部门指定的代理人
- **系统管理员**：可查看所有确认任务，紧急情况下可代为处理

**详细操作步骤：**

**步骤1：接收确认通知**
- 联系人通过企业微信收到访客申请确认通知
- 通知内容包含：
  - 访客基本信息（姓名、单位、联系方式）
  - 来访事由和访问时间
  - 申请单号和紧急程度
  - 快速确认链接和详情查看链接
- 同时发送短信备用通知（包含申请单号和联系方式）

**步骤2：查看申请详情**
- 联系人点击通知链接进入确认页面
- 系统显示完整的申请信息：
  - 访客详细信息和上传的证件照片
  - 来访目的和具体事由描述
  - 访问时间、地点和预期活动安排
  - 车辆信息和同行人员信息
  - 特殊需求和注意事项
- 提供申请信息的打印和导出功能

**步骤3：信息核实验证**
- **身份核实**：
  - 确认访客身份的真实性和合法性
  - 验证来访单位的真实性
  - 检查访客是否为已知联系人
- **事由核实**：
  - 确认来访事由的真实性和必要性
  - 验证访问内容是否符合部门业务范围
  - 评估访问活动的安全风险等级
- **时间核实**：
  - 确认访问时间的可行性
  - 检查是否与其他重要活动冲突
  - 评估接待能力和资源安排

**步骤4：做出确认决策**
- **同意确认**：
  - 确认访客申请，流程继续进行
  - 可添加确认备注和特殊要求
  - 可修改访问时间和访问区域（在合理范围内）
  - 可指定具体的接待安排和注意事项
- **拒绝确认**：
  - 选择拒绝原因（预设选项或自定义）
  - 填写详细的拒绝说明
  - 可提供替代方案或建议
- **申请修改**：
  - 要求访客修改部分申请信息
  - 指定需要修改的具体内容
  - 设置修改期限

**业务规则：**
- 联系人必须在24小时内做出响应
- 超时未响应自动升级给部门主管处理
- 联系人可以修改访问时间（±2小时范围内）
- 联系人可以调整访问区域（本部门管辖范围内）
- 拒绝确认必须填写具体原因
- 同一访客在同一时间段只能有一个有效申请

**异常情况处理：**
- **联系人不在岗**：
  - 系统自动检测联系人状态（请假、出差等）
  - 自动转发给预设的代理人或部门主管
  - 发送通知给申请人说明情况
- **联系人信息错误**：
  - 申请人可申请修改联系人信息
  - 需要新联系人确认接受申请
  - 原联系人收到变更通知
- **重复申请处理**：
  - 系统自动识别同一访客的重复申请
  - 提示联系人是否合并处理
  - 保留最新申请，标记重复申请状态
- **紧急申请处理**：
  - 标记为紧急的申请优先通知
  - 缩短确认时限为4小时
  - 同时通知联系人和部门主管

**确认完成后的系统处理：**
- **确认通过**：
  - **记录确认操作**：在`visitor_process_operation`表中记录
    - 操作类型：联系人确认
    - 操作结果：通过
    - 操作人：联系人
    - 确认意见和修改内容
  - **记录状态变更**：在`visitor_status_history`表中记录状态变更
  - **完成Flowable任务**：调用TaskService完成确认任务
  - **启动下级审批**：根据访客类型创建相应的审批任务
  - 发送确认通知给申请人
- **确认拒绝**：
  - **记录拒绝操作**：详细记录拒绝原因和建议
  - **记录状态变更**：状态变更为"已驳回"
  - **终止工作流程**：结束Flowable流程实例
  - 发送拒绝通知给申请人（包含拒绝原因）
  - 流程结束，支持重新申请
- **申请修改**：
  - **记录跟进信息**：在`visitor_process_follow`表中记录
    - 跟进类型：申请修改要求
    - 跟进内容：具体修改要求
    - 下次跟进时间：修改期限
  - 发送修改要求给申请人
  - 申请状态保持"待确认"
  - 修改完成后重新进入确认流程

#### 3. 部门审批阶段

**触发条件：**
- 联系人确认通过访客申请
- 系统根据访客类型自动分配审批任务
- 审批人收到审批通知

**角色权限：**
- **部门主管**：审批普通访客和施工承包商的部门级审批
- **综管部人员**：审批政府访客和所有访客的终审
- **安全部门人员**：审批施工承包商的安全评估
- **系统管理员**：可查看所有审批任务，紧急情况下可代为处理

**审批流程分类：**
- **普通访客**：联系人确认 → 部门主管审批 → 综管部终审
- **政府访客**：联系人确认 → 综管部直接审批
- **施工承包商**：联系人确认 → 安全部门审批 → 部门主管审批 → 综管部终审

**详细操作步骤：**

**步骤1：接收审批任务**
- 审批人通过企业微信收到审批通知
- 通知内容包含：
  - 申请单号和访客基本信息
  - 审批类型和紧急程度
  - 联系人确认意见
  - 快速审批链接和详情查看链接
- 系统在审批人的待办任务中显示新的审批任务

**步骤2：审查申请信息**
- **基础信息审查**：
  - 访客身份和资质审查
  - 来访单位的合法性和信誉度
  - 来访事由的合理性和必要性
  - 访问时间和地点的适宜性
- **安全风险评估**：
  - 访客的安全背景调查
  - 访问内容的敏感度评估
  - 访问区域的安全等级匹配
  - 同行人员的安全风险评估
- **资源可行性评估**：
  - 接待能力和资源安排
  - 访问期间的其他活动冲突
  - 特殊需求的满足能力
  - 安全保障措施的充分性

**步骤3：做出审批决策**
- **审批通过**：
  - 确认同意访客申请
  - 可添加审批意见和特殊要求
  - 可调整访问权限和范围
  - 可指定安全保障措施
- **审批驳回**：
  - 选择驳回原因（预设选项或自定义）
  - 填写详细的驳回说明
  - 可提供改进建议或替代方案
- **退回修改**：
  - 要求补充或修改申请信息
  - 指定需要补充的具体内容
  - 设置补充期限

**业务规则：**
- 每级审批必须在48小时内完成
- 超时自动升级或按规定处理
- 审批人可以退回申请要求补充信息
- 特殊情况可以启用加急审批流程
- 同一级别可以设置多人会签
- 审批意见必须明确具体

**异常情况处理：**
- **审批人不在岗**：
  - 系统自动检测审批人状态
  - 转发给预设的代理人
  - 通知相关人员审批人变更
- **审批冲突**：
  - 多人会签时出现不同意见
  - 升级给更高级别审批人决策
  - 记录冲突原因和解决方案
- **信息不足**：
  - 退回申请要求补充信息
  - 暂停审批流程等待补充
  - 设置补充期限和提醒
- **紧急申请**：
  - 缩短审批时限为24小时
  - 同时通知多个审批人
  - 支持电话或现场审批确认

**审批完成后的系统处理：**
- **审批通过**：
  - 进入下一级审批或生成二维码
  - 发送审批通过通知
  - 更新申请状态
  - 记录审批意见和时间
- **审批驳回**：
  - 发送驳回通知给申请人和联系人
  - 更新申请状态为"已驳回"
  - 记录驳回原因和建议
  - 流程结束，支持重新申请
- **退回修改**：
  - 发送修改要求给申请人
  - 申请状态保持当前审批阶段
  - 设置修改期限和提醒
  - 修改完成后继续当前审批

#### 4. 二维码生成阶段

**触发条件：**
- 所有必要的审批流程完成且结果为通过
- 访客已完成必要的安全培训（如适用）
- 访问时间未过期且在有效期内
- 系统确认具备生成二维码的条件

**详细操作步骤：**

**步骤1：生成JWT安全令牌**
- 编码访客基本信息（姓名、电话、身份证号）
- 编码访问权限信息（时间、区域、车辆权限）
- 编码安全信息（过期时间、权限范围、防伪码）
- 使用AES加密算法生成安全令牌

**步骤2：生成二维码图片**
- 使用ZXing库将JWT Token转换为QR Code
- 设置二维码参数（300x300像素，H级纠错）
- 添加访客信息文字（姓名、单位、有效期）
- 添加防伪元素（Logo水印、时间戳）

**步骤3：发送通知**
- 通过短信发送二维码给访客
- 通过企业微信通知联系人审批结果
- 抄送门岗警卫访客预到通知
- 记录发送状态和时间

**业务规则：**
- 二维码有效期为访问结束时间+2小时
- 每个申请只能生成一个有效二维码
- 二维码只能使用一次入园
- 支持离线验证和在线校验双重模式

**异常处理：**
- 生成失败：自动重试3次，记录错误日志
- 发送失败：提供重发功能，支持多种方式
- 过期处理：自动监控，过期前提醒

#### 5. 入园验证阶段

**触发条件：**
- 访客到达园区门岗准备入园
- 出示有效的二维码进行验证
- 在二维码有效期内进行验证
- 门岗警卫具备扫码验证权限

**详细操作步骤：**

**步骤1：二维码扫描验证**
- 警卫使用扫码设备扫描访客二维码
- 系统解析JWT Token获取访客信息
- 验证二维码有效性和完整性
- 检查是否已被使用过

**步骤2：身份信息核实**
- 要求访客出示身份证件
- 对比证件信息与申请信息一致性
- 拍摄访客现场照片记录
- 核实车辆信息（如驾车）

**步骤3：安全检查程序**
- 测量体温并记录
- 进行必要的安全检查
- 确认安全培训完成状态
- 签署安全承诺书

**步骤4：入园手续办理**
- 记录实际入园时间和门岗位置
- 发放临时通行证（如需要）
- 提供路线指引和注意事项
- 更新系统访客状态

**业务规则：**
- 二维码只能使用一次，扫码后立即失效
- 体温异常（≥37.3°C）者不得入园
- 安全培训必须完成才能入园
- 身份信息不符者不得入园

**异常处理：**
- 二维码无效：记录异常并联系确认
- 身份不符：详细记录并联系申请人
- 健康异常：按防疫要求处理
- 系统故障：启用手动登记备用方案

#### 6. 出园登记阶段

**触发条件：**
- 访客完成访问准备离园
- 到达门岗进行出园登记
- 在规定时间内完成出园手续

**详细操作步骤：**

**步骤1：出园身份确认**
- 扫描二维码或核实身份证确认访客身份
- 确认为已入园的访客
- 检查访问时间是否超时

**步骤2：物品检查**
- 检查携带物品是否符合规定
- 确认无违禁物品带出
- 特殊物品需要证明或登记

**步骤3：异常情况记录**
- 记录超时未出园情况
- 记录物品丢失或损坏情况
- 记录其他异常情况

**步骤4：出园手续办理**
- 记录出园时间和门岗位置
- 回收临时通行证
- 拍照记录（如有异常）

**业务规则：**
- 必须在访问结束时间后2小时内出园
- 超时出园需要说明原因
- 异常情况必须详细记录
- 出园后二维码自动失效

**异常处理：**
- 超时出园：记录原因，通知相关人员
- 物品异常：详细记录，必要时报告
- 拒绝配合：联系安保和联系人

**流程完成后处理：**
- 更新申请状态为"已出园"
- 发送出园通知给联系人
- 生成访问记录报告
- 归档相关资料

**触发条件：**
- 访客完成访问准备离园
- 到达门岗进行出园登记

**角色权限：**
- 门岗警卫：出园登记、异常处理
- 联系人：确认访客正常离园

**具体操作步骤：**
1. **出园验证**：确认访客身份
   - 扫描二维码或核实身份证
   - 确认为已入园的访客
   - 检查是否有未完成事项

2. **物品检查**：检查携带物品
   - 确认无违禁物品带出
   - 检查是否有园区物品
   - 特殊物品需要证明

3. **异常情况处理**：记录异常情况
   - 超时未出园的处理
   - 物品丢失或损坏
   - 其他异常情况

4. **出园登记**：完成出园手续
   - 记录出园时间和门岗
   - 拍照记录（如有异常）
   - 回收临时通行证

**业务规则：**
- 必须在访问结束时间后2小时内出园
- 超时出园需要说明原因
- 异常情况必须详细记录
- 出园后二维码自动失效

**异常处理：**
- 超时出园：记录原因，通知相关人员
- 物品异常：详细记录，必要时报告
- 拒绝配合：联系安保和联系人

**后续操作：**
- 更新申请状态为"已出园"
- 发送出园通知给联系人
- 生成访问记录报告
- 归档相关资料

### 7. 业务流程深度细化

#### A. 异常场景处理决策树

**访客申请异常处理决策树**

```mermaid
graph TD
    A[访客申请异常] --> B{异常类型判断}

    B -->|信息不完整| C[信息完整性检查]
    B -->|身份验证失败| D[身份验证异常]
    B -->|黑名单访客| E[安全风险处理]
    B -->|系统故障| F[技术故障处理]
    B -->|超时未处理| G[超时处理]

    C --> C1{缺失字段类型}
    C1 -->|必填字段缺失| C2[要求补充信息]
    C1 -->|格式错误| C3[格式校验提示]
    C1 -->|逻辑冲突| C4[业务规则检查]

    D --> D1{身份证验证}
    D1 -->|格式错误| D2[提示正确格式]
    D1 -->|号码无效| D3[要求重新输入]
    D1 -->|照片不清晰| D4[要求重新上传]

    E --> E1{风险等级评估}
    E1 -->|高风险| E2[直接拒绝+报告]
    E1 -->|中风险| E3[人工审核]
    E1 -->|低风险| E4[特殊审批流程]

    F --> F1{故障类型}
    F1 -->|数据库故障| F2[启用备用数据源]
    F1 -->|网络故障| F3[离线模式处理]
    F1 -->|服务故障| F4[降级服务]

    G --> G1{超时阶段}
    G1 -->|联系人确认超时| G2[自动升级给主管]
    G1 -->|审批超时| G3[发送催办通知]
    G1 -->|入园超时| G4[二维码自动失效]
```

#### B. 角色权限矩阵详细说明

| 角色 | 申请提交 | 信息查看 | 联系人确认 | 部门审批 | 综管审批 | 安全审批 | 二维码生成 | 入园登记 | 出园登记 | 异常处理 |
|------|----------|----------|------------|----------|----------|----------|------------|----------|----------|----------|
| **访客** | ✓ | 自己的申请 | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ | 申请修改 |
| **联系人** | 代为申请 | 相关申请 | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ | 沟通协调 |
| **部门主管** | 代为申请 | 部门申请 | 代理确认 | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | 部门异常 |
| **综管部** | ✓ | 所有申请 | 代理确认 | 代理审批 | ✓ | ✗ | ✓ | ✗ | ✗ | 流程异常 |
| **安全部** | ✓ | 安全相关 | ✗ | ✗ | ✗ | ✓ | ✗ | ✗ | ✗ | 安全异常 |
| **门岗警卫** | ✗ | 当日访客 | ✗ | ✗ | ✗ | ✗ | ✗ | ✓ | ✓ | 现场异常 |
| **系统管理员** | ✓ | 所有数据 | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | 所有异常 |

**权限细化说明：**

**访客权限：**
- 只能提交自己的申请
- 只能查看自己的申请状态和历史
- 可以在"待确认"状态下修改申请信息
- 可以取消未开始审批的申请
- 不能查看其他访客的信息

**联系人权限：**
- 可以代为提交访客申请
- 可以查看指定自己为联系人的所有申请
- 必须在24小时内确认或拒绝申请
- 可以修改访问时间（±2小时范围内）
- 可以添加接待备注和特殊要求

**部门主管权限：**
- 可以审批本部门联系人确认的申请
- 可以查看本部门所有访客申请
- 可以在联系人不在时代为确认
- 可以设置部门访客管理规则
- 可以处理本部门的访客异常情况

**综管部权限：**
- 拥有最高的业务权限
- 可以审批所有类型的访客申请
- 可以生成和管理二维码
- 可以处理跨部门的协调问题
- 可以制定全园区的访客管理政策

#### C. 业务规则详细说明

**1. 访问时间规则**

```
访问时间验证算法：
IF 访问开始时间 < 当前时间 + 30分钟 THEN
    返回错误："访问开始时间不能早于当前时间30分钟"
END IF

IF 访问结束时间 <= 访问开始时间 THEN
    返回错误："访问结束时间必须晚于开始时间"
END IF

IF 访问结束时间 - 访问开始时间 > 7天 THEN
    返回错误："单次访问时长不能超过7天"
END IF

IF 访问开始时间.hour < 6 OR 访问开始时间.hour > 22 THEN
    IF 访客类型 != 政府访客 AND 访客类型 != 紧急访客 THEN
        返回警告："非工作时间访问需要特殊审批"
    END IF
END IF
```

**2. 同行人员规则**

```
同行人员验证算法：
IF 同行人数量 > 10 THEN
    返回错误："同行人数量不能超过10人"
END IF

IF 访客类型 == 施工承包商 THEN
    FOR EACH 同行人员 IN 同行人员列表 DO
        IF 同行人员.身份证号 == NULL THEN
            返回错误："施工承包商的同行人员必须提供身份证号"
        END IF
    END FOR
END IF

IF 同行人员中存在重复身份证号 THEN
    返回错误："同行人员身份证号不能重复"
END IF
```

**3. 车辆管理规则**

```
车辆信息验证算法：
IF 是否驾车 == 是 THEN
    IF 车牌号格式不正确 THEN
        返回错误："车牌号格式不正确"
    END IF

    IF 车牌号 IN 黑名单车辆 THEN
        返回错误："该车辆被列入黑名单，禁止入园"
    END IF

    IF 访客类型 == 施工承包商 THEN
        IF 行驶证编号 == NULL THEN
            返回错误："施工车辆必须提供行驶证编号"
        END IF
    END IF

    // 检查车辆是否已有其他有效申请
    IF EXISTS(其他有效申请 WHERE 车牌号 == 当前车牌号 AND 时间重叠) THEN
        返回警告："该车辆在此时间段已有其他访问申请"
    END IF
END IF
```

**4. 审批流程规则**

```
审批流程选择算法：
SWITCH 访客类型 DO
    CASE 普通访客:
        流程 = [联系人确认 → 部门主管审批 → 综管部终审]

    CASE 政府访客:
        流程 = [联系人确认 → 综管部直接审批]

    CASE 施工承包商:
        流程 = [联系人确认 → 安全部审批 → 部门主管审批 → 综管部终审]

    CASE 紧急访客:
        流程 = [联系人确认 → 综管部加急审批]
        审批时限 = 4小时

    DEFAULT:
        返回错误："未知的访客类型"
END SWITCH

// 审批时限控制
FOR EACH 审批节点 IN 流程 DO
    IF 审批节点.类型 == 加急 THEN
        审批节点.时限 = 4小时
    ELSE IF 审批节点.类型 == 普通 THEN
        审批节点.时限 = 48小时
    END IF
END FOR
```

#### D. 边界条件处理

**1. 并发访问控制**

```
并发控制算法：
// 同一联系人同时段申请限制
MAX_CONCURRENT_APPLICATIONS_PER_CONTACT = 5

IF COUNT(当前联系人的进行中申请) >= MAX_CONCURRENT_APPLICATIONS_PER_CONTACT THEN
    返回错误："同一联系人同时段申请数量不能超过5个"
END IF

// 同一访客重复申请控制
IF EXISTS(相同访客 WHERE 状态 IN [待确认, 已审批, 已入园] AND 时间重叠) THEN
    返回错误："该访客在此时间段已有有效申请"
END IF

// 园区容量控制
当日访客总数 = COUNT(当日所有已审批申请)
IF 当日访客总数 + 当前申请人数 > 园区日最大容量 THEN
    返回警告："当日访客数量接近上限，建议调整访问时间"
END IF
```

**2. 数据完整性边界**

```
数据完整性检查算法：
// 必填字段检查
必填字段列表 = [访客姓名, 联系电话, 来访单位, 来访事由, 联系人, 访问时间, 访问区域]

FOR EACH 字段 IN 必填字段列表 DO
    IF 字段值 == NULL OR 字段值 == "" THEN
        返回错误："字段[" + 字段名 + "]为必填项"
    END IF
END FOR

// 字段长度检查
字段长度限制 = {
    访客姓名: [2, 50],
    联系电话: [11, 11],
    来访单位: [2, 100],
    来访事由: [10, 200]
}

FOR EACH 字段 IN 字段长度限制 DO
    IF 字段值.length < 最小长度 OR 字段值.length > 最大长度 THEN
        返回错误："字段[" + 字段名 + "]长度不符合要求"
    END IF
END FOR
```

### 8. 技术实现深度细化

#### A. 关键业务逻辑算法

**1. 二维码生成和验证算法**

```java
/**
 * 访客二维码生成算法
 */
public class VisitorQrCodeService {

    private static final String SECRET_KEY = "visitor_qrcode_secret_2024";
    private static final int QR_CODE_SIZE = 300;
    private static final int EXPIRE_HOURS = 24;

    /**
     * 生成访客二维码
     */
    public String generateVisitorQrCode(VisitorApplicationDO application) {
        try {
            // 1. 构建JWT载荷
            Map<String, Object> payload = buildQrCodePayload(application);

            // 2. 生成JWT Token
            String jwtToken = generateJwtToken(payload);

            // 3. 生成二维码图片
            BufferedImage qrCodeImage = generateQrCodeImage(jwtToken);

            // 4. 添加访客信息水印
            BufferedImage finalImage = addVisitorWatermark(qrCodeImage, application);

            // 5. 上传到文件服务器
            String qrCodeUrl = uploadQrCodeImage(finalImage, application.getApplicationNo());

            // 6. 记录二维码信息
            recordQrCodeInfo(application.getId(), jwtToken, qrCodeUrl);

            return qrCodeUrl;

        } catch (Exception e) {
            log.error("生成访客二维码失败", e);
            throw new ServiceException("二维码生成失败，请重试");
        }
    }

    /**
     * 构建二维码载荷
     */
    private Map<String, Object> buildQrCodePayload(VisitorApplicationDO application) {
        Map<String, Object> payload = new HashMap<>();

        // 基础信息
        payload.put("applicationId", application.getId());
        payload.put("applicationNo", application.getApplicationNo());
        payload.put("visitorName", application.getVisitorName());
        payload.put("visitorPhone", application.getVisitorPhone());
        payload.put("companyName", application.getCompanyName());

        // 访问权限
        payload.put("visitStartTime", application.getVisitStartTime().getTime());
        payload.put("visitEndTime", application.getVisitEndTime().getTime());
        payload.put("visitArea", application.getVisitArea());
        payload.put("hasVehicle", application.getHasVehicle());
        payload.put("vehiclePlate", application.getVehiclePlate());

        // 安全信息
        payload.put("issueTime", System.currentTimeMillis());
        payload.put("expireTime", System.currentTimeMillis() + EXPIRE_HOURS * 3600 * 1000);
        payload.put("version", "2.0");
        payload.put("checksum", calculateChecksum(payload));

        return payload;
    }

    /**
     * 验证访客二维码
     */
    public QrCodeValidationResult validateVisitorQrCode(String qrCodeContent) {
        try {
            // 1. 解析JWT Token
            Claims claims = parseJwtToken(qrCodeContent);

            // 2. 验证基础信息
            validateBasicInfo(claims);

            // 3. 验证时间有效性
            validateTimeValidity(claims);

            // 4. 验证使用状态
            validateUsageStatus(claims);

            // 5. 验证校验和
            validateChecksum(claims);

            // 6. 构建验证结果
            return buildValidationResult(claims, true, "验证通过");

        } catch (ExpiredJwtException e) {
            return buildValidationResult(null, false, "二维码已过期");
        } catch (SignatureException e) {
            return buildValidationResult(null, false, "二维码签名无效");
        } catch (Exception e) {
            log.error("二维码验证失败", e);
            return buildValidationResult(null, false, "二维码验证失败");
        }
    }

    /**
     * 计算校验和
     */
    private String calculateChecksum(Map<String, Object> payload) {
        StringBuilder sb = new StringBuilder();
        payload.entrySet().stream()
            .filter(entry -> !"checksum".equals(entry.getKey()))
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> sb.append(entry.getKey()).append(entry.getValue()));

        return DigestUtils.md5Hex(sb.toString() + SECRET_KEY);
    }
}
```

**2. 流程状态机算法**

```java
/**
 * 访客申请状态机
 */
public class VisitorStatusMachine {

    // 状态转换映射表
    private static final Map<Integer, Set<Integer>> STATUS_TRANSITIONS = new HashMap<>();

    static {
        // 待确认 -> 已审批、已驳回
        STATUS_TRANSITIONS.put(VisitorStatusEnum.PENDING_CONFIRM.getStatus(),
            Set.of(VisitorStatusEnum.APPROVED.getStatus(), VisitorStatusEnum.REJECTED.getStatus()));

        // 已审批 -> 已入园、已驳回
        STATUS_TRANSITIONS.put(VisitorStatusEnum.APPROVED.getStatus(),
            Set.of(VisitorStatusEnum.CHECKED_IN.getStatus(), VisitorStatusEnum.REJECTED.getStatus()));

        // 已入园 -> 已出园
        STATUS_TRANSITIONS.put(VisitorStatusEnum.CHECKED_IN.getStatus(),
            Set.of(VisitorStatusEnum.CHECKED_OUT.getStatus()));
    }

    /**
     * 状态转换验证
     */
    public boolean canTransition(Integer fromStatus, Integer toStatus) {
        Set<Integer> allowedTransitions = STATUS_TRANSITIONS.get(fromStatus);
        return allowedTransitions != null && allowedTransitions.contains(toStatus);
    }

    /**
     * 执行状态转换
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeStatusTransition(Long applicationId, Integer toStatus,
                                      String reason, Long operatorId) {
        // 1. 获取当前状态
        VisitorApplicationDO application = visitorApplicationMapper.selectById(applicationId);
        Integer fromStatus = application.getStatus();

        // 2. 验证状态转换合法性
        if (!canTransition(fromStatus, toStatus)) {
            throw new ServiceException("状态转换不合法：从" + fromStatus + "到" + toStatus);
        }

        // 3. 执行状态转换前的业务逻辑
        executePreTransitionLogic(application, toStatus);

        // 4. 更新申请状态
        application.setStatus(toStatus);
        application.setUpdateTime(LocalDateTime.now());
        visitorApplicationMapper.updateById(application);

        // 5. 记录状态变更历史
        recordStatusHistory(applicationId, fromStatus, toStatus, reason, operatorId);

        // 6. 执行状态转换后的业务逻辑
        executePostTransitionLogic(application, fromStatus, toStatus);

        // 7. 发送状态变更通知
        sendStatusChangeNotification(application, fromStatus, toStatus);
    }

    /**
     * 状态转换前置逻辑
     */
    private void executePreTransitionLogic(VisitorApplicationDO application, Integer toStatus) {
        switch (toStatus) {
            case 2: // 已审批
                // 生成二维码
                generateQrCodeForApplication(application);
                break;
            case 4: // 已入园
                // 验证二维码有效性
                validateQrCodeForEntry(application);
                break;
            case 5: // 已出园
                // 计算访问时长
                calculateVisitDuration(application);
                break;
        }
    }
}
```

#### B. 数据校验规则详细实现

**1. 统一数据校验框架**

```java
/**
 * 访客申请数据校验器
 */
@Component
public class VisitorApplicationValidator {

    @Resource
    private BlacklistService blacklistService;

    @Resource
    private DepartmentService departmentService;

    /**
     * 执行完整的数据校验
     */
    public ValidationResult validate(VisitorApplicationSaveReqVO reqVO) {
        ValidationResult result = new ValidationResult();

        // 1. 基础字段校验
        validateBasicFields(reqVO, result);

        // 2. 业务规则校验
        validateBusinessRules(reqVO, result);

        // 3. 安全校验
        validateSecurity(reqVO, result);

        // 4. 数据一致性校验
        validateDataConsistency(reqVO, result);

        return result;
    }

    /**
     * 基础字段校验
     */
    private void validateBasicFields(VisitorApplicationSaveReqVO reqVO, ValidationResult result) {
        // 访客姓名校验
        if (StringUtils.isBlank(reqVO.getVisitorName())) {
            result.addError("visitorName", "访客姓名不能为空");
        } else if (reqVO.getVisitorName().length() < 2 || reqVO.getVisitorName().length() > 50) {
            result.addError("visitorName", "访客姓名长度必须在2-50个字符之间");
        } else if (!isValidChineseName(reqVO.getVisitorName())) {
            result.addError("visitorName", "访客姓名格式不正确");
        }

        // 联系电话校验
        if (StringUtils.isBlank(reqVO.getVisitorPhone())) {
            result.addError("visitorPhone", "联系电话不能为空");
        } else if (!isValidPhoneNumber(reqVO.getVisitorPhone())) {
            result.addError("visitorPhone", "联系电话格式不正确");
        }

        // 身份证号校验（施工承包商必填）
        if (reqVO.getVisitorType() == VisitorTypeEnum.CONTRACTOR.getType()) {
            if (StringUtils.isBlank(reqVO.getIdCard())) {
                result.addError("idCard", "施工承包商必须提供身份证号");
            } else if (!isValidIdCard(reqVO.getIdCard())) {
                result.addError("idCard", "身份证号格式不正确");
            }
        }

        // 车牌号校验
        if (reqVO.getHasVehicle() != null && reqVO.getHasVehicle() == 1) {
            if (StringUtils.isBlank(reqVO.getVehiclePlate())) {
                result.addError("vehiclePlate", "驾车访问必须提供车牌号");
            } else if (!isValidVehiclePlate(reqVO.getVehiclePlate())) {
                result.addError("vehiclePlate", "车牌号格式不正确");
            }
        }
    }

    /**
     * 业务规则校验
     */
    private void validateBusinessRules(VisitorApplicationSaveReqVO reqVO, ValidationResult result) {
        // 访问时间校验
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime visitStart = reqVO.getVisitStartTime();
        LocalDateTime visitEnd = reqVO.getVisitEndTime();

        if (visitStart == null || visitEnd == null) {
            result.addError("visitTime", "访问时间不能为空");
            return;
        }

        if (visitStart.isBefore(now.plusMinutes(30))) {
            result.addError("visitStartTime", "访问开始时间不能早于当前时间30分钟");
        }

        if (visitEnd.isBefore(visitStart)) {
            result.addError("visitEndTime", "访问结束时间必须晚于开始时间");
        }

        long visitDurationHours = Duration.between(visitStart, visitEnd).toHours();
        if (visitDurationHours > 168) { // 7天
            result.addError("visitTime", "单次访问时长不能超过7天");
        }

        // 同行人员校验
        if (reqVO.getCompanionCount() != null && reqVO.getCompanionCount() > 10) {
            result.addError("companionCount", "同行人数量不能超过10人");
        }

        // 联系人校验
        if (!departmentService.isValidContact(reqVO.getContactPerson(), reqVO.getContactDeptId())) {
            result.addError("contactPerson", "联系人信息无效");
        }
    }

    /**
     * 安全校验
     */
    private void validateSecurity(VisitorApplicationSaveReqVO reqVO, ValidationResult result) {
        // 黑名单校验
        if (blacklistService.isInBlacklist(reqVO.getVisitorPhone())) {
            result.addError("security", "该访客被列入黑名单，禁止申请");
        }

        if (StringUtils.isNotBlank(reqVO.getIdCard()) &&
            blacklistService.isInBlacklist(reqVO.getIdCard())) {
            result.addError("security", "该身份证号被列入黑名单，禁止申请");
        }

        if (StringUtils.isNotBlank(reqVO.getVehiclePlate()) &&
            blacklistService.isVehicleInBlacklist(reqVO.getVehiclePlate())) {
            result.addError("security", "该车辆被列入黑名单，禁止入园");
        }

        // 敏感词检查
        if (containsSensitiveWords(reqVO.getVisitReason())) {
            result.addError("visitReason", "来访事由包含敏感词汇");
        }
    }

    /**
     * 正则表达式校验方法
     */
    private boolean isValidChineseName(String name) {
        return name.matches("^[\\u4e00-\\u9fa5·]{2,50}$");
    }

    private boolean isValidPhoneNumber(String phone) {
        return phone.matches("^1[3-9]\\d{9}$");
    }

    private boolean isValidIdCard(String idCard) {
        return idCard.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    }

    private boolean isValidVehiclePlate(String plate) {
        // 支持新能源车牌和普通车牌
        return plate.matches("^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$");
    }
}
```

#### C. 缓存策略详细设计

**1. 多级缓存架构**

```java
/**
 * 访客管理缓存服务
 */
@Service
public class VisitorCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private CaffeineCache localCache;

    // 缓存键前缀
    private static final String CACHE_PREFIX = "visitor:";
    private static final String APPLICATION_CACHE_KEY = CACHE_PREFIX + "application:";
    private static final String QR_CODE_CACHE_KEY = CACHE_PREFIX + "qrcode:";
    private static final String BLACKLIST_CACHE_KEY = CACHE_PREFIX + "blacklist:";

    // 缓存过期时间
    private static final Duration APPLICATION_CACHE_TTL = Duration.ofMinutes(30);
    private static final Duration QR_CODE_CACHE_TTL = Duration.ofHours(24);
    private static final Duration BLACKLIST_CACHE_TTL = Duration.ofHours(1);

    /**
     * 缓存访客申请信息
     */
    public void cacheApplication(VisitorApplicationDO application) {
        String key = APPLICATION_CACHE_KEY + application.getId();

        // L1缓存：本地缓存
        localCache.put(key, application, APPLICATION_CACHE_TTL);

        // L2缓存：Redis缓存
        redisTemplate.opsForValue().set(key, application, APPLICATION_CACHE_TTL);
    }

    /**
     * 获取缓存的访客申请信息
     */
    public VisitorApplicationDO getCachedApplication(Long applicationId) {
        String key = APPLICATION_CACHE_KEY + applicationId;

        // 先查本地缓存
        VisitorApplicationDO application = localCache.get(key, VisitorApplicationDO.class);
        if (application != null) {
            return application;
        }

        // 再查Redis缓存
        application = (VisitorApplicationDO) redisTemplate.opsForValue().get(key);
        if (application != null) {
            // 回写本地缓存
            localCache.put(key, application, APPLICATION_CACHE_TTL);
            return application;
        }

        return null;
    }

    /**
     * 缓存二维码验证结果
     */
    public void cacheQrCodeValidation(String qrCodeContent, QrCodeValidationResult result) {
        String key = QR_CODE_CACHE_KEY + DigestUtils.md5Hex(qrCodeContent);
        redisTemplate.opsForValue().set(key, result, QR_CODE_CACHE_TTL);
    }

    /**
     * 获取缓存的二维码验证结果
     */
    public QrCodeValidationResult getCachedQrCodeValidation(String qrCodeContent) {
        String key = QR_CODE_CACHE_KEY + DigestUtils.md5Hex(qrCodeContent);
        return (QrCodeValidationResult) redisTemplate.opsForValue().get(key);
    }

    /**
     * 缓存黑名单信息
     */
    public void cacheBlacklist(String identifier, boolean isBlacklisted) {
        String key = BLACKLIST_CACHE_KEY + identifier;
        redisTemplate.opsForValue().set(key, isBlacklisted, BLACKLIST_CACHE_TTL);
    }

    /**
     * 检查黑名单缓存
     */
    public Boolean getCachedBlacklistStatus(String identifier) {
        String key = BLACKLIST_CACHE_KEY + identifier;
        return (Boolean) redisTemplate.opsForValue().get(key);
    }

    /**
     * 清除相关缓存
     */
    public void evictApplicationCache(Long applicationId) {
        String key = APPLICATION_CACHE_KEY + applicationId;
        localCache.evict(key);
        redisTemplate.delete(key);
    }

    /**
     * 预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        // 预热黑名单缓存
        List<String> blacklist = blacklistService.getAllBlacklistItems();
        blacklist.forEach(item -> cacheBlacklist(item, true));

        // 预热常用配置缓存
        warmUpConfigurationCache();
    }
}
```

#### D. 性能优化方案

**1. 数据库查询优化**

```sql
-- 创建复合索引优化常用查询
CREATE INDEX idx_visitor_app_status_time ON visitor_application(status, visit_start_time, tenant_id);
CREATE INDEX idx_visitor_app_contact_time ON visitor_application(contact_person, create_time, tenant_id);
CREATE INDEX idx_visitor_record_time_type ON visitor_record(operation_time, operation_type, tenant_id);

-- 分区表设计（按月分区）
CREATE TABLE visitor_application_202501 PARTITION OF visitor_application
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 查询优化示例
-- 优化前：全表扫描
SELECT * FROM visitor_application WHERE status = 1 ORDER BY create_time DESC LIMIT 20;

-- 优化后：使用索引
SELECT id, application_no, visitor_name, status, create_time
FROM visitor_application
WHERE status = 1 AND tenant_id = ?
ORDER BY create_time DESC
LIMIT 20;
```

**2. 异步处理优化**

```java
/**
 * 异步任务处理服务
 */
@Service
public class VisitorAsyncService {

    @Async("visitorTaskExecutor")
    public CompletableFuture<Void> sendNotificationAsync(Long applicationId, NotificationType type) {
        try {
            // 异步发送通知
            notificationService.sendNotification(applicationId, type);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("异步发送通知失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Async("visitorTaskExecutor")
    public CompletableFuture<String> generateQrCodeAsync(Long applicationId) {
        try {
            // 异步生成二维码
            String qrCodeUrl = qrCodeService.generateQrCode(applicationId);
            return CompletableFuture.completedFuture(qrCodeUrl);
        } catch (Exception e) {
            log.error("异步生成二维码失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @EventListener
    @Async("visitorTaskExecutor")
    public void handleApplicationStatusChange(ApplicationStatusChangeEvent event) {
        // 异步处理状态变更事件
        processStatusChangeLogic(event);
    }
}

/**
 * 线程池配置
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean("visitorTaskExecutor")
    public TaskExecutor visitorTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("visitor-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

### 9. 集成方案深度细化

#### A. 企业微信集成详细实现

**1. 企业微信API封装服务**

```java
/**
 * 企业微信集成服务
 */
@Service
@Slf4j
public class WeChatWorkService {

    @Value("${wechat.work.corp-id}")
    private String corpId;

    @Value("${wechat.work.agent-id}")
    private String agentId;

    @Value("${wechat.work.secret}")
    private String secret;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String ACCESS_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    private static final String SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send";
    private static final String TOKEN_CACHE_KEY = "wechat:work:token";
    private static final Duration TOKEN_CACHE_TTL = Duration.ofMinutes(110); // 微信token有效期2小时，提前10分钟刷新

    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        // 先从缓存获取
        String cachedToken = redisTemplate.opsForValue().get(TOKEN_CACHE_KEY);
        if (StringUtils.isNotBlank(cachedToken)) {
            return cachedToken;
        }

        // 从微信API获取新token
        try {
            String url = ACCESS_TOKEN_URL + "?corpid=" + corpId + "&corpsecret=" + secret;
            WeChatTokenResponse response = restTemplate.getForObject(url, WeChatTokenResponse.class);

            if (response != null && response.getErrcode() == 0) {
                String accessToken = response.getAccessToken();
                // 缓存token
                redisTemplate.opsForValue().set(TOKEN_CACHE_KEY, accessToken, TOKEN_CACHE_TTL);
                return accessToken;
            } else {
                throw new ServiceException("获取微信访问令牌失败：" + (response != null ? response.getErrmsg() : "未知错误"));
            }
        } catch (Exception e) {
            log.error("获取微信访问令牌异常", e);
            throw new ServiceException("获取微信访问令牌失败");
        }
    }

    /**
     * 发送访客申请通知
     */
    public void sendVisitorApplicationNotification(VisitorApplicationDO application, String toUser) {
        try {
            WeChatMessage message = buildApplicationNotificationMessage(application, toUser);
            sendMessage(message);
        } catch (Exception e) {
            log.error("发送访客申请通知失败", e);
            // 记录发送失败，后续重试
            recordNotificationFailure(application.getId(), toUser, "application_notification", e.getMessage());
        }
    }

    /**
     * 发送审批结果通知
     */
    public void sendApprovalResultNotification(VisitorApplicationDO application, String result, String reason) {
        try {
            WeChatMessage message = buildApprovalResultMessage(application, result, reason);
            sendMessage(message);
        } catch (Exception e) {
            log.error("发送审批结果通知失败", e);
            recordNotificationFailure(application.getId(), application.getCreator(), "approval_result", e.getMessage());
        }
    }

    /**
     * 构建访客申请通知消息
     */
    private WeChatMessage buildApplicationNotificationMessage(VisitorApplicationDO application, String toUser) {
        WeChatMessage message = new WeChatMessage();
        message.setTouser(toUser);
        message.setMsgtype("textcard");
        message.setAgentid(Integer.parseInt(agentId));

        WeChatTextCard textCard = new WeChatTextCard();
        textCard.setTitle("访客申请待确认");
        textCard.setDescription(String.format(
            "访客：%s\n单位：%s\n事由：%s\n时间：%s 至 %s\n请及时处理",
            application.getVisitorName(),
            application.getCompanyName(),
            application.getVisitReason(),
            application.getVisitStartTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")),
            application.getVisitEndTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm"))
        ));
        textCard.setUrl("https://park.company.com/visitor/confirm?id=" + application.getId());
        textCard.setBtntxt("立即处理");

        message.setTextcard(textCard);
        return message;
    }

    /**
     * 发送消息到企业微信
     */
    private void sendMessage(WeChatMessage message) {
        String accessToken = getAccessToken();
        String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<WeChatMessage> entity = new HttpEntity<>(message, headers);

        WeChatMessageResponse response = restTemplate.postForObject(url, entity, WeChatMessageResponse.class);

        if (response == null || response.getErrcode() != 0) {
            throw new ServiceException("发送微信消息失败：" + (response != null ? response.getErrmsg() : "未知错误"));
        }
    }

    /**
     * 重试发送失败的通知
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void retryFailedNotifications() {
        List<NotificationFailureRecord> failedRecords = getFailedNotifications();

        for (NotificationFailureRecord record : failedRecords) {
            try {
                // 重新发送通知
                resendNotification(record);
                // 标记为已处理
                markNotificationAsProcessed(record.getId());
            } catch (Exception e) {
                log.warn("重试发送通知失败：{}", record.getId(), e);
                // 增加重试次数
                incrementRetryCount(record.getId());
            }
        }
    }
}
```

**2. 短信服务集成实现**

```java
/**
 * 短信服务集成
 */
@Service
@Slf4j
public class SmsService {

    @Value("${sms.aliyun.access-key-id}")
    private String accessKeyId;

    @Value("${sms.aliyun.access-key-secret}")
    private String accessKeySecret;

    @Value("${sms.aliyun.sign-name}")
    private String signName;

    @Resource
    private IAcsClient acsClient;

    // 短信模板配置
    private static final Map<String, String> SMS_TEMPLATES = new HashMap<>();

    static {
        SMS_TEMPLATES.put("APPLICATION_SUBMITTED", "SMS_001"); // 申请提交通知
        SMS_TEMPLATES.put("APPROVAL_RESULT", "SMS_002");       // 审批结果通知
        SMS_TEMPLATES.put("QR_CODE_GENERATED", "SMS_003");     // 二维码生成通知
        SMS_TEMPLATES.put("VISIT_REMINDER", "SMS_004");        // 访问提醒
    }

    /**
     * 发送访客申请提交通知
     */
    public void sendApplicationSubmittedSms(VisitorApplicationDO application) {
        Map<String, String> params = new HashMap<>();
        params.put("visitorName", application.getVisitorName());
        params.put("applicationNo", application.getApplicationNo());
        params.put("visitDate", application.getVisitStartTime().format(DateTimeFormatter.ofPattern("MM月dd日")));

        sendSms(application.getVisitorPhone(), "APPLICATION_SUBMITTED", params);
    }

    /**
     * 发送审批结果通知
     */
    public void sendApprovalResultSms(VisitorApplicationDO application, boolean approved) {
        Map<String, String> params = new HashMap<>();
        params.put("visitorName", application.getVisitorName());
        params.put("result", approved ? "通过" : "驳回");
        params.put("applicationNo", application.getApplicationNo());

        if (approved) {
            params.put("visitTime", application.getVisitStartTime().format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm")));
        }

        sendSms(application.getVisitorPhone(), "APPROVAL_RESULT", params);
    }

    /**
     * 发送二维码生成通知
     */
    public void sendQrCodeGeneratedSms(VisitorApplicationDO application, String qrCodeUrl) {
        Map<String, String> params = new HashMap<>();
        params.put("visitorName", application.getVisitorName());
        params.put("qrCodeUrl", qrCodeUrl);
        params.put("visitTime", application.getVisitStartTime().format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm")));
        params.put("visitArea", application.getVisitArea());

        sendSms(application.getVisitorPhone(), "QR_CODE_GENERATED", params);
    }

    /**
     * 通用短信发送方法
     */
    private void sendSms(String phoneNumber, String templateType, Map<String, String> params) {
        try {
            String templateCode = SMS_TEMPLATES.get(templateType);
            if (templateCode == null) {
                throw new ServiceException("未找到短信模板：" + templateType);
            }

            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phoneNumber);
            request.setSignName(signName);
            request.setTemplateCode(templateCode);
            request.setTemplateParam(JSON.toJSONString(params));

            SendSmsResponse response = acsClient.getAcsResponse(request);

            if (!"OK".equals(response.getCode())) {
                throw new ServiceException("短信发送失败：" + response.getMessage());
            }

            // 记录发送成功日志
            recordSmsLog(phoneNumber, templateType, params, true, null);

        } catch (Exception e) {
            log.error("发送短信失败", e);
            // 记录发送失败日志
            recordSmsLog(phoneNumber, templateType, params, false, e.getMessage());
            throw new ServiceException("短信发送失败");
        }
    }

    /**
     * 批量发送短信
     */
    @Async("smsTaskExecutor")
    public CompletableFuture<Void> sendBatchSms(List<SmsTask> smsTasks) {
        for (SmsTask task : smsTasks) {
            try {
                sendSms(task.getPhoneNumber(), task.getTemplateType(), task.getParams());
                Thread.sleep(100); // 避免发送过快
            } catch (Exception e) {
                log.error("批量发送短信失败：{}", task.getPhoneNumber(), e);
            }
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 短信发送频率限制
     */
    public boolean checkSendingFrequency(String phoneNumber, String templateType) {
        String key = "sms:frequency:" + phoneNumber + ":" + templateType;
        String count = redisTemplate.opsForValue().get(key);

        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(1));
            return true;
        }

        int currentCount = Integer.parseInt(count);
        if (currentCount >= 5) { // 每分钟最多5条
            return false;
        }

        redisTemplate.opsForValue().increment(key);
        return true;
    }
}
```

#### B. 错误处理和重试机制

**1. 统一错误处理框架**

```java
/**
 * 集成服务错误处理器
 */
@Component
@Slf4j
public class IntegrationErrorHandler {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 处理企业微信API错误
     */
    public void handleWeChatError(Exception e, String operation, Object... params) {
        if (e instanceof WeChatApiException) {
            WeChatApiException weChatError = (WeChatApiException) e;

            switch (weChatError.getErrorCode()) {
                case 40014: // 无效的access_token
                    // 清除缓存的token，下次重新获取
                    redisTemplate.delete("wechat:work:token");
                    scheduleRetry(operation, params, 1);
                    break;

                case 45009: // 接口调用超过限制
                    // 延迟重试
                    scheduleRetry(operation, params, 60);
                    break;

                case 43004: // 需要接收者关注
                    // 记录用户未关注，不重试
                    recordPermanentFailure(operation, "用户未关注企业微信", params);
                    break;

                default:
                    // 其他错误，短时间后重试
                    scheduleRetry(operation, params, 5);
            }
        } else {
            // 网络错误等，重试
            scheduleRetry(operation, params, 5);
        }
    }

    /**
     * 处理短信服务错误
     */
    public void handleSmsError(Exception e, String phoneNumber, String templateType, Map<String, String> params) {
        if (e instanceof SmsApiException) {
            SmsApiException smsError = (SmsApiException) e;

            switch (smsError.getErrorCode()) {
                case "isv.BUSINESS_LIMIT_CONTROL":
                    // 业务限流，延迟重试
                    scheduleRetry("sendSms", new Object[]{phoneNumber, templateType, params}, 300);
                    break;

                case "isv.MOBILE_NUMBER_ILLEGAL":
                    // 手机号非法，不重试
                    recordPermanentFailure("sendSms", "手机号非法", phoneNumber, templateType);
                    break;

                case "isv.AMOUNT_NOT_ENOUGH":
                    // 余额不足，发送告警
                    sendBalanceAlert();
                    recordPermanentFailure("sendSms", "短信余额不足", phoneNumber, templateType);
                    break;

                default:
                    // 其他错误，重试
                    scheduleRetry("sendSms", new Object[]{phoneNumber, templateType, params}, 30);
            }
        }
    }

    /**
     * 指数退避重试策略
     */
    @Retryable(
        value = {Exception.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void executeWithRetry(Runnable operation) {
        operation.run();
    }

    /**
     * 熔断器模式
     */
    @CircuitBreaker(name = "wechat-api", fallbackMethod = "wechatFallback")
    public void callWeChatApi(String operation, Object... params) {
        // 调用微信API
    }

    public void wechatFallback(String operation, Object[] params, Exception e) {
        log.warn("微信API调用失败，启用降级方案：{}", operation, e);
        // 降级方案：使用短信或邮件替代
        useFallbackNotification(operation, params);
    }
}
```

**2. 重试队列实现**

```java
/**
 * 重试队列服务
 */
@Service
public class RetryQueueService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RabbitTemplate rabbitTemplate;

    private static final String RETRY_QUEUE = "visitor.retry.queue";
    private static final String DELAY_EXCHANGE = "visitor.delay.exchange";

    /**
     * 添加重试任务
     */
    public void addRetryTask(RetryTask task) {
        task.setRetryCount(task.getRetryCount() + 1);
        task.setNextRetryTime(calculateNextRetryTime(task.getRetryCount()));

        if (task.getRetryCount() <= task.getMaxRetries()) {
            // 发送到延迟队列
            long delay = task.getNextRetryTime().getTime() - System.currentTimeMillis();
            rabbitTemplate.convertAndSend(DELAY_EXCHANGE, RETRY_QUEUE, task, message -> {
                message.getMessageProperties().setDelay((int) delay);
                return message;
            });
        } else {
            // 超过最大重试次数，记录失败
            recordFinalFailure(task);
        }
    }

    /**
     * 处理重试任务
     */
    @RabbitListener(queues = RETRY_QUEUE)
    public void handleRetryTask(RetryTask task) {
        try {
            executeRetryTask(task);
        } catch (Exception e) {
            log.warn("重试任务执行失败：{}", task.getTaskId(), e);
            addRetryTask(task); // 重新加入重试队列
        }
    }

    /**
     * 计算下次重试时间（指数退避）
     */
    private Date calculateNextRetryTime(int retryCount) {
        long delay = (long) Math.pow(2, retryCount) * 1000; // 2^n 秒
        delay = Math.min(delay, 300000); // 最大5分钟
        return new Date(System.currentTimeMillis() + delay);
    }
}
```

#### C. 数据同步一致性方案

**1. 分布式事务处理**

```java
/**
 * 分布式事务服务
 */
@Service
public class DistributedTransactionService {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 使用Saga模式处理分布式事务
     */
    public void executeDistributedTransaction(VisitorApplicationDO application) {
        String sagaId = UUID.randomUUID().toString();
        SagaTransaction saga = new SagaTransaction(sagaId);

        try {
            // 步骤1：更新业务数据
            saga.addStep("updateApplication", () -> updateApplication(application));

            // 步骤2：发送微信通知
            saga.addStep("sendWeChatNotification", () -> sendWeChatNotification(application));

            // 步骤3：发送短信通知
            saga.addStep("sendSmsNotification", () -> sendSmsNotification(application));

            // 步骤4：记录操作日志
            saga.addStep("recordOperationLog", () -> recordOperationLog(application));

            // 执行Saga事务
            saga.execute();

        } catch (Exception e) {
            log.error("分布式事务执行失败，开始补偿", e);
            // 执行补偿操作
            saga.compensate();
        }
    }

    /**
     * 最终一致性检查
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkEventualConsistency() {
        // 检查业务数据与外部系统的一致性
        List<InconsistentRecord> inconsistentRecords = findInconsistentRecords();

        for (InconsistentRecord record : inconsistentRecords) {
            try {
                reconcileData(record);
            } catch (Exception e) {
                log.error("数据一致性修复失败：{}", record.getId(), e);
            }
        }
    }

    /**
     * 幂等性保证
     */
    public void executeIdempotentOperation(String operationId, Runnable operation) {
        String lockKey = "idempotent:lock:" + operationId;
        String resultKey = "idempotent:result:" + operationId;

        // 检查是否已执行
        if (redisTemplate.hasKey(resultKey)) {
            return; // 已执行，直接返回
        }

        // 获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofMinutes(5));

        if (Boolean.TRUE.equals(lockAcquired)) {
            try {
                // 再次检查是否已执行
                if (!redisTemplate.hasKey(resultKey)) {
                    operation.run();
                    // 标记为已执行
                    redisTemplate.opsForValue().set(resultKey, "completed", Duration.ofHours(24));
                }
            } finally {
                redisTemplate.delete(lockKey);
            }
        } else {
            // 获取锁失败，等待后重试
            try {
                Thread.sleep(100);
                executeIdempotentOperation(operationId, operation);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}
```

### 10. 用户体验深度细化

#### A. 前端交互流程详细设计

**1. 访客申请页面交互流程**

```javascript
/**
 * 访客申请页面交互控制器
 */
class VisitorApplicationController {

    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.formData = {};
        this.validationRules = this.initValidationRules();
        this.autoSaveTimer = null;
    }

    /**
     * 初始化页面
     */
    init() {
        this.initStepIndicator();
        this.initFormValidation();
        this.initAutoSave();
        this.initLocationService();
        this.bindEvents();
    }

    /**
     * 步骤指示器
     */
    initStepIndicator() {
        const steps = [
            { title: '基本信息', icon: 'user' },
            { title: '访问信息', icon: 'calendar' },
            { title: '车辆信息', icon: 'car' },
            { title: '同行人员', icon: 'team' },
            { title: '确认提交', icon: 'check' }
        ];

        this.renderStepIndicator(steps);
    }

    /**
     * 实时表单验证
     */
    initFormValidation() {
        // 访客姓名验证
        $('#visitorName').on('blur', (e) => {
            const value = e.target.value.trim();
            const result = this.validateVisitorName(value);
            this.showFieldValidation('visitorName', result);
        });

        // 联系电话验证
        $('#visitorPhone').on('input', (e) => {
            const value = e.target.value.replace(/\D/g, '');
            e.target.value = this.formatPhoneNumber(value);

            if (value.length === 11) {
                const result = this.validatePhoneNumber(value);
                this.showFieldValidation('visitorPhone', result);
            }
        });

        // 身份证号验证
        $('#idCard').on('blur', (e) => {
            const value = e.target.value.trim();
            if (value) {
                const result = this.validateIdCard(value);
                this.showFieldValidation('idCard', result);
            }
        });

        // 车牌号验证
        $('#vehiclePlate').on('blur', (e) => {
            const value = e.target.value.trim().toUpperCase();
            e.target.value = value;
            if (value) {
                const result = this.validateVehiclePlate(value);
                this.showFieldValidation('vehiclePlate', result);
            }
        });
    }

    /**
     * 自动保存功能
     */
    initAutoSave() {
        // 监听表单变化
        $('form').on('input change', () => {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = setTimeout(() => {
                this.autoSaveFormData();
            }, 3000); // 3秒后自动保存
        });

        // 页面离开前保存
        window.addEventListener('beforeunload', () => {
            this.autoSaveFormData();
        });
    }

    /**
     * 地理位置服务
     */
    initLocationService() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                    };
                },
                (error) => {
                    console.warn('获取位置失败：', error);
                }
            );
        }
    }

    /**
     * 下一步操作
     */
    nextStep() {
        if (this.validateCurrentStep()) {
            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.renderCurrentStep();
                this.updateStepIndicator();
                this.scrollToTop();
            } else {
                this.submitApplication();
            }
        }
    }

    /**
     * 上一步操作
     */
    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.renderCurrentStep();
            this.updateStepIndicator();
            this.scrollToTop();
        }
    }

    /**
     * 智能表单填充
     */
    smartFormFill() {
        // 根据访客类型自动调整表单
        const visitorType = $('#visitorType').val();

        switch (visitorType) {
            case '1': // 普通访客
                this.hideField('idCard');
                this.hideField('contractorProject');
                break;
            case '2': // 政府访客
                this.showField('idCard');
                this.hideField('contractorProject');
                break;
            case '3': // 施工承包商
                this.showField('idCard');
                this.showField('contractorProject');
                this.requireField('idCard');
                break;
        }
    }

    /**
     * 联系人智能搜索
     */
    initContactSearch() {
        $('#contactPerson').autocomplete({
            source: (request, response) => {
                $.ajax({
                    url: '/api/contacts/search',
                    data: { keyword: request.term },
                    success: (data) => {
                        response(data.map(item => ({
                            label: `${item.name} - ${item.deptName}`,
                            value: item.name,
                            data: item
                        })));
                    }
                });
            },
            select: (event, ui) => {
                this.fillContactInfo(ui.item.data);
            }
        });
    }

    /**
     * 图片上传处理
     */
    initImageUpload() {
        // 访客照片上传
        $('#visitorPhotoUpload').on('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.validateImageFile(file, (isValid, message) => {
                    if (isValid) {
                        this.uploadImage(file, 'visitorPhoto');
                    } else {
                        this.showError(message);
                    }
                });
            }
        });

        // 支持拖拽上传
        $('.upload-area').on('dragover', (e) => {
            e.preventDefault();
            $(e.target).addClass('drag-over');
        }).on('dragleave', (e) => {
            $(e.target).removeClass('drag-over');
        }).on('drop', (e) => {
            e.preventDefault();
            $(e.target).removeClass('drag-over');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageUpload(files[0]);
            }
        });
    }

    /**
     * 表单验证方法
     */
    validateVisitorName(name) {
        if (!name) {
            return { valid: false, message: '访客姓名不能为空' };
        }
        if (name.length < 2 || name.length > 50) {
            return { valid: false, message: '访客姓名长度必须在2-50个字符之间' };
        }
        if (!/^[\u4e00-\u9fa5·]{2,50}$/.test(name)) {
            return { valid: false, message: '访客姓名格式不正确' };
        }
        return { valid: true, message: '' };
    }

    validatePhoneNumber(phone) {
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            return { valid: false, message: '手机号格式不正确' };
        }
        return { valid: true, message: '' };
    }

    validateIdCard(idCard) {
        const pattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!pattern.test(idCard)) {
            return { valid: false, message: '身份证号格式不正确' };
        }

        // 校验位验证
        if (!this.validateIdCardChecksum(idCard)) {
            return { valid: false, message: '身份证号校验位不正确' };
        }

        return { valid: true, message: '' };
    }

    /**
     * 提交申请
     */
    async submitApplication() {
        this.showLoading('正在提交申请...');

        try {
            const formData = this.collectFormData();
            const response = await this.apiCall('/api/visitor/application/create', formData);

            if (response.success) {
                this.showSuccess('申请提交成功！', () => {
                    window.location.href = '/visitor/application/success?id=' + response.data;
                });
            } else {
                this.showError(response.message);
            }
        } catch (error) {
            this.showError('提交失败，请重试');
        } finally {
            this.hideLoading();
        }
    }
}
```

**2. 移动端适配方案**

```css
/* 移动端响应式设计 */
@media (max-width: 768px) {
    .visitor-form {
        padding: 10px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
        border-radius: 6px;
    }

    .step-indicator {
        flex-direction: column;
        gap: 8px;
    }

    .step-item {
        font-size: 12px;
        padding: 8px;
    }

    .upload-area {
        min-height: 120px;
        padding: 20px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        padding: 12px;
        font-size: 16px;
    }
}

/* 触摸优化 */
.touch-device .form-control {
    min-height: 44px; /* iOS推荐最小触摸区域 */
}

.touch-device .btn {
    min-height: 44px;
    padding: 12px 20px;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .visitor-form {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .form-control {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }

    .form-control:focus {
        background-color: #2d2d2d;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
}
```

#### B. 用户引导和提示设计

**1. 新手引导系统**

```javascript
/**
 * 新手引导服务
 */
class UserGuideService {

    constructor() {
        this.currentStep = 0;
        this.guideSteps = this.initGuideSteps();
    }

    /**
     * 初始化引导步骤
     */
    initGuideSteps() {
        return [
            {
                target: '#visitorType',
                title: '选择访客类型',
                content: '请根据您的来访目的选择合适的访客类型。不同类型的审批流程可能不同。',
                position: 'bottom'
            },
            {
                target: '#visitorName',
                title: '填写访客信息',
                content: '请准确填写访客的真实姓名，这将用于身份验证。',
                position: 'bottom'
            },
            {
                target: '#contactPerson',
                title: '选择联系人',
                content: '请选择园区内的联系人，他们将收到确认通知。您可以搜索联系人姓名。',
                position: 'bottom'
            },
            {
                target: '#visitTime',
                title: '设置访问时间',
                content: '请设置准确的访问时间，二维码将在此时间段内有效。',
                position: 'top'
            },
            {
                target: '.upload-area',
                title: '上传证件照片',
                content: '请上传清晰的身份证照片，支持拖拽上传。照片将用于入园时的身份核实。',
                position: 'top'
            }
        ];
    }

    /**
     * 开始引导
     */
    startGuide() {
        if (this.isFirstTimeUser()) {
            this.showGuideStep(0);
        }
    }

    /**
     * 显示引导步骤
     */
    showGuideStep(stepIndex) {
        if (stepIndex >= this.guideSteps.length) {
            this.completeGuide();
            return;
        }

        const step = this.guideSteps[stepIndex];
        const $target = $(step.target);

        if ($target.length === 0) {
            this.nextStep();
            return;
        }

        // 高亮目标元素
        this.highlightElement($target);

        // 显示提示框
        this.showTooltip($target, step);

        this.currentStep = stepIndex;
    }

    /**
     * 智能提示系统
     */
    showSmartTips() {
        // 根据用户行为显示相关提示
        this.bindSmartTips();
    }

    bindSmartTips() {
        // 访客类型选择提示
        $('#visitorType').on('change', (e) => {
            const type = e.target.value;
            const tips = this.getVisitorTypeTips(type);
            this.showInlineTip(e.target, tips);
        });

        // 时间选择提示
        $('#visitStartTime').on('change', (e) => {
            const selectedTime = new Date(e.target.value);
            const now = new Date();

            if (selectedTime.getHours() < 8 || selectedTime.getHours() > 18) {
                this.showWarningTip(e.target, '非工作时间访问可能需要特殊审批');
            }

            if (selectedTime.getDay() === 0 || selectedTime.getDay() === 6) {
                this.showWarningTip(e.target, '周末访问请提前联系相关人员');
            }
        });

        // 车牌号输入提示
        $('#vehiclePlate').on('focus', (e) => {
            this.showInlineTip(e.target, '请输入完整的车牌号，如：粤B12345');
        });
    }

    /**
     * 错误提示优化
     */
    showFieldError(fieldName, message) {
        const $field = $(`#${fieldName}`);
        const $errorDiv = $field.siblings('.field-error');

        if ($errorDiv.length === 0) {
            $field.after(`<div class="field-error">${message}</div>`);
        } else {
            $errorDiv.text(message);
        }

        $field.addClass('error');

        // 自动清除错误状态
        $field.on('input.clearError', () => {
            $field.removeClass('error');
            $field.siblings('.field-error').remove();
            $field.off('input.clearError');
        });
    }
}
```

#### C. 无障碍访问支持

**1. 无障碍功能实现**

```javascript
/**
 * 无障碍访问服务
 */
class AccessibilityService {

    constructor() {
        this.init();
    }

    init() {
        this.addAriaLabels();
        this.addKeyboardNavigation();
        this.addScreenReaderSupport();
        this.addHighContrastMode();
        this.addFontSizeControl();
    }

    /**
     * 添加ARIA标签
     */
    addAriaLabels() {
        // 为表单字段添加描述
        $('input, select, textarea').each(function() {
            const $this = $(this);
            const label = $this.siblings('label').text() || $this.attr('placeholder');

            if (label && !$this.attr('aria-label')) {
                $this.attr('aria-label', label);
            }
        });

        // 为按钮添加描述
        $('button').each(function() {
            const $this = $(this);
            if (!$this.attr('aria-label') && $this.text()) {
                $this.attr('aria-label', $this.text());
            }
        });

        // 为步骤指示器添加描述
        $('.step-item').each(function(index) {
            $(this).attr('aria-label', `第${index + 1}步：${$(this).text()}`);
        });
    }

    /**
     * 键盘导航支持
     */
    addKeyboardNavigation() {
        // Tab键导航
        $('input, select, textarea, button').attr('tabindex', '0');

        // 回车键提交
        $('form').on('keypress', (e) => {
            if (e.which === 13 && !$(e.target).is('textarea')) {
                e.preventDefault();
                this.handleEnterKey();
            }
        });

        // ESC键取消
        $(document).on('keydown', (e) => {
            if (e.which === 27) {
                this.handleEscapeKey();
            }
        });

        // 方向键导航步骤
        $(document).on('keydown', (e) => {
            if (e.altKey) {
                switch (e.which) {
                    case 37: // Alt + 左箭头
                        this.prevStep();
                        break;
                    case 39: // Alt + 右箭头
                        this.nextStep();
                        break;
                }
            }
        });
    }

    /**
     * 屏幕阅读器支持
     */
    addScreenReaderSupport() {
        // 添加live region用于动态内容
        $('body').append('<div id="sr-live" aria-live="polite" class="sr-only"></div>');

        // 表单验证消息
        this.announceValidationErrors = (message) => {
            $('#sr-live').text(message);
        };

        // 页面状态变化
        this.announcePageChange = (message) => {
            $('#sr-live').text(message);
        };
    }

    /**
     * 高对比度模式
     */
    addHighContrastMode() {
        // 检测系统高对比度设置
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            $('body').addClass('high-contrast');
        }

        // 手动切换高对比度
        $('#toggleHighContrast').on('click', () => {
            $('body').toggleClass('high-contrast');
            localStorage.setItem('highContrast', $('body').hasClass('high-contrast'));
        });
    }

    /**
     * 字体大小控制
     */
    addFontSizeControl() {
        const savedFontSize = localStorage.getItem('fontSize') || 'normal';
        this.setFontSize(savedFontSize);

        $('#fontSizeControl').on('change', (e) => {
            const fontSize = e.target.value;
            this.setFontSize(fontSize);
            localStorage.setItem('fontSize', fontSize);
        });
    }

    setFontSize(size) {
        $('body').removeClass('font-small font-normal font-large font-xlarge');
        $('body').addClass(`font-${size}`);
    }
}
```

### 11. 运维监控深度细化

#### A. 系统监控指标设计

**1. 业务监控指标**

```java
/**
 * 访客管理业务监控服务
 */
@Service
@Slf4j
public class VisitorMonitoringService {

    @Resource
    private MeterRegistry meterRegistry;

    @Resource
    private VisitorApplicationMapper applicationMapper;

    // 业务指标计数器
    private final Counter applicationSubmittedCounter;
    private final Counter applicationApprovedCounter;
    private final Counter applicationRejectedCounter;
    private final Counter qrCodeGeneratedCounter;
    private final Counter visitorCheckedInCounter;
    private final Counter visitorCheckedOutCounter;

    // 业务指标计时器
    private final Timer approvalProcessTimer;
    private final Timer qrCodeGenerationTimer;
    private final Timer checkInProcessTimer;

    // 业务指标仪表盘
    private final Gauge pendingApplicationsGauge;
    private final Gauge dailyVisitorCountGauge;
    private final Gauge averageApprovalTimeGauge;

    public VisitorMonitoringService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化计数器
        this.applicationSubmittedCounter = Counter.builder("visitor.application.submitted")
            .description("访客申请提交总数")
            .register(meterRegistry);

        this.applicationApprovedCounter = Counter.builder("visitor.application.approved")
            .description("访客申请审批通过总数")
            .register(meterRegistry);

        this.applicationRejectedCounter = Counter.builder("visitor.application.rejected")
            .description("访客申请审批驳回总数")
            .register(meterRegistry);

        // 初始化计时器
        this.approvalProcessTimer = Timer.builder("visitor.approval.process.time")
            .description("审批流程处理时间")
            .register(meterRegistry);

        this.qrCodeGenerationTimer = Timer.builder("visitor.qrcode.generation.time")
            .description("二维码生成时间")
            .register(meterRegistry);

        // 初始化仪表盘
        this.pendingApplicationsGauge = Gauge.builder("visitor.application.pending.count")
            .description("待处理申请数量")
            .register(meterRegistry, this, VisitorMonitoringService::getPendingApplicationCount);

        this.dailyVisitorCountGauge = Gauge.builder("visitor.daily.count")
            .description("当日访客数量")
            .register(meterRegistry, this, VisitorMonitoringService::getDailyVisitorCount);
    }

    /**
     * 记录申请提交
     */
    public void recordApplicationSubmitted(String visitorType, String department) {
        applicationSubmittedCounter.increment(
            Tags.of(
                "visitor_type", visitorType,
                "department", department
            )
        );
    }

    /**
     * 记录审批结果
     */
    public void recordApprovalResult(String result, String approverRole, long processingTimeMs) {
        if ("approved".equals(result)) {
            applicationApprovedCounter.increment(Tags.of("approver_role", approverRole));
        } else {
            applicationRejectedCounter.increment(Tags.of("approver_role", approverRole));
        }

        approvalProcessTimer.record(processingTimeMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 记录二维码生成
     */
    public void recordQrCodeGeneration(long generationTimeMs, boolean success) {
        qrCodeGeneratedCounter.increment(Tags.of("success", String.valueOf(success)));

        if (success) {
            qrCodeGenerationTimer.record(generationTimeMs, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 记录入园签到
     */
    public void recordVisitorCheckIn(String gateLocation, String verificationMethod) {
        visitorCheckedInCounter.increment(
            Tags.of(
                "gate_location", gateLocation,
                "verification_method", verificationMethod
            )
        );
    }

    /**
     * 获取待处理申请数量
     */
    private double getPendingApplicationCount() {
        return applicationMapper.countByStatus(VisitorStatusEnum.PENDING_CONFIRM.getStatus());
    }

    /**
     * 获取当日访客数量
     */
    private double getDailyVisitorCount() {
        LocalDate today = LocalDate.now();
        return applicationMapper.countByDateRange(today.atStartOfDay(), today.plusDays(1).atStartOfDay());
    }

    /**
     * 自定义业务告警
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkBusinessAlerts() {
        // 检查待处理申请积压
        long pendingCount = getPendingApplicationCount();
        if (pendingCount > 50) {
            sendAlert("PENDING_APPLICATIONS_HIGH",
                "待处理申请数量过多：" + pendingCount,
                AlertLevel.WARNING);
        }

        // 检查审批超时
        List<VisitorApplicationDO> overdueApplications = findOverdueApplications();
        if (!overdueApplications.isEmpty()) {
            sendAlert("APPROVAL_OVERDUE",
                "发现" + overdueApplications.size() + "个超时未审批的申请",
                AlertLevel.CRITICAL);
        }

        // 检查二维码生成失败率
        double qrCodeFailureRate = calculateQrCodeFailureRate();
        if (qrCodeFailureRate > 0.1) { // 失败率超过10%
            sendAlert("QRCODE_FAILURE_RATE_HIGH",
                "二维码生成失败率过高：" + String.format("%.2f%%", qrCodeFailureRate * 100),
                AlertLevel.WARNING);
        }
    }
}
```

**2. 技术监控指标**

```java
/**
 * 技术监控服务
 */
@Service
public class TechnicalMonitoringService {

    @Resource
    private MeterRegistry meterRegistry;

    @Resource
    private DataSource dataSource;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 数据库连接池监控
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initDatabaseMonitoring() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;

            Gauge.builder("database.connection.active")
                .description("活跃数据库连接数")
                .register(meterRegistry, hikariDataSource, HikariDataSource::getActiveConnections);

            Gauge.builder("database.connection.idle")
                .description("空闲数据库连接数")
                .register(meterRegistry, hikariDataSource, HikariDataSource::getIdleConnections);

            Gauge.builder("database.connection.total")
                .description("总数据库连接数")
                .register(meterRegistry, hikariDataSource, HikariDataSource::getTotalConnections);
        }
    }

    /**
     * Redis连接监控
     */
    @Scheduled(fixedDelay = 30000) // 每30秒检查一次
    public void monitorRedisConnection() {
        try {
            long startTime = System.currentTimeMillis();
            redisTemplate.opsForValue().get("health_check");
            long responseTime = System.currentTimeMillis() - startTime;

            Timer.Sample sample = Timer.start(meterRegistry);
            sample.stop(Timer.builder("redis.response.time")
                .description("Redis响应时间")
                .register(meterRegistry));

            // 记录Redis可用性
            Gauge.builder("redis.availability")
                .description("Redis可用性")
                .register(meterRegistry, this, (self) -> 1.0);

        } catch (Exception e) {
            log.error("Redis连接检查失败", e);

            Gauge.builder("redis.availability")
                .description("Redis可用性")
                .register(meterRegistry, this, (self) -> 0.0);

            sendAlert("REDIS_CONNECTION_FAILED", "Redis连接失败", AlertLevel.CRITICAL);
        }
    }

    /**
     * JVM监控
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initJvmMonitoring() {
        // 内存使用监控
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

        Gauge.builder("jvm.memory.heap.used")
            .description("JVM堆内存使用量")
            .register(meterRegistry, memoryBean,
                bean -> bean.getHeapMemoryUsage().getUsed());

        Gauge.builder("jvm.memory.heap.max")
            .description("JVM堆内存最大值")
            .register(meterRegistry, memoryBean,
                bean -> bean.getHeapMemoryUsage().getMax());

        // GC监控
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            Gauge.builder("jvm.gc.collection.count")
                .description("GC收集次数")
                .tag("gc_name", gcBean.getName())
                .register(meterRegistry, gcBean, GarbageCollectorMXBean::getCollectionCount);

            Gauge.builder("jvm.gc.collection.time")
                .description("GC收集时间")
                .tag("gc_name", gcBean.getName())
                .register(meterRegistry, gcBean, GarbageCollectorMXBean::getCollectionTime);
        }
    }

    /**
     * API性能监控
     */
    @Around("@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public Object monitorApiPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            Object result = joinPoint.proceed();

            // 记录成功调用
            Counter.builder("api.request.count")
                .description("API请求次数")
                .tag("class", className)
                .tag("method", methodName)
                .tag("status", "success")
                .register(meterRegistry)
                .increment();

            return result;

        } catch (Exception e) {
            // 记录失败调用
            Counter.builder("api.request.count")
                .description("API请求次数")
                .tag("class", className)
                .tag("method", methodName)
                .tag("status", "error")
                .register(meterRegistry)
                .increment();

            throw e;

        } finally {
            sample.stop(Timer.builder("api.request.duration")
                .description("API请求耗时")
                .tag("class", className)
                .tag("method", methodName)
                .register(meterRegistry));
        }
    }
}
```

#### B. 日志记录规范

**1. 结构化日志设计**

```java
/**
 * 访客管理日志服务
 */
@Service
@Slf4j
public class VisitorLoggingService {

    private static final String LOG_PREFIX = "[VISITOR]";

    /**
     * 记录业务操作日志
     */
    public void logBusinessOperation(String operation, Long applicationId, Long userId,
                                   Object requestData, Object responseData, long duration) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("operation", operation);
        logData.put("applicationId", applicationId);
        logData.put("userId", userId);
        logData.put("duration", duration);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("requestData", requestData);
        logData.put("responseData", responseData);

        log.info("{} 业务操作 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录安全事件日志
     */
    public void logSecurityEvent(String eventType, String description, String ipAddress,
                               String userAgent, Long userId) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("eventType", eventType);
        logData.put("description", description);
        logData.put("ipAddress", ipAddress);
        logData.put("userAgent", userAgent);
        logData.put("userId", userId);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("severity", "HIGH");

        log.warn("{} 安全事件 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录系统错误日志
     */
    public void logSystemError(String errorType, String errorMessage, String stackTrace,
                             Map<String, Object> context) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("errorType", errorType);
        logData.put("errorMessage", errorMessage);
        logData.put("stackTrace", stackTrace);
        logData.put("context", context);
        logData.put("timestamp", System.currentTimeMillis());
        logData.put("severity", "ERROR");

        log.error("{} 系统错误 - {}", LOG_PREFIX, JSON.toJSONString(logData));
    }

    /**
     * 记录性能日志
     */
    public void logPerformance(String operation, long duration, Map<String, Object> metrics) {
        if (duration > 5000) { // 超过5秒记录慢查询
            Map<String, Object> logData = new HashMap<>();
            logData.put("operation", operation);
            logData.put("duration", duration);
            logData.put("metrics", metrics);
            logData.put("timestamp", System.currentTimeMillis());
            logData.put("type", "SLOW_OPERATION");

            log.warn("{} 性能告警 - {}", LOG_PREFIX, JSON.toJSONString(logData));
        }
    }
}
```

**2. 日志配置**

```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>

    <!-- 业务日志文件 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/visitor-business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/visitor-business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <arguments/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[VISITOR] 业务操作")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 安全日志文件 -->
    <appender name="SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/visitor-security.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/visitor-security.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <message/>
                <mdc/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>message.contains("[VISITOR] 安全事件")</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
    </appender>

    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/visitor-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/visitor-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <stackTrace/>
            </providers>
        </encoder>
        <filter class="ch.qos.logback.threshold.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="BUSINESS_FILE"/>
        <appender-ref ref="SECURITY_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>
```

#### C. 故障排查和应急处理

**1. 故障诊断工具**

```java
/**
 * 故障诊断服务
 */
@RestController
@RequestMapping("/api/admin/diagnosis")
@PreAuthorize("hasRole('ADMIN')")
public class DiagnosisController {

    @Resource
    private VisitorApplicationMapper applicationMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private DataSource dataSource;

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();

        // 数据库连接检查
        health.put("database", checkDatabaseHealth());

        // Redis连接检查
        health.put("redis", checkRedisHealth());

        // 业务数据检查
        health.put("business", checkBusinessHealth());

        // 外部服务检查
        health.put("external", checkExternalServicesHealth());

        return Result.success(health);
    }

    /**
     * 性能诊断
     */
    @GetMapping("/performance")
    public Result<Map<String, Object>> performanceDiagnosis() {
        Map<String, Object> performance = new HashMap<>();

        // 数据库性能
        performance.put("database", analyzeDatabasePerformance());

        // 缓存性能
        performance.put("cache", analyzeCachePerformance());

        // JVM性能
        performance.put("jvm", analyzeJvmPerformance());

        return Result.success(performance);
    }

    /**
     * 数据一致性检查
     */
    @GetMapping("/consistency")
    public Result<Map<String, Object>> consistencyCheck() {
        Map<String, Object> consistency = new HashMap<>();

        // 检查业务数据与流程数据一致性
        List<String> inconsistentApplications = findInconsistentApplications();
        consistency.put("inconsistentApplications", inconsistentApplications);

        // 检查缓存数据一致性
        List<String> inconsistentCache = findInconsistentCacheData();
        consistency.put("inconsistentCache", inconsistentCache);

        return Result.success(consistency);
    }

    /**
     * 修复数据不一致
     */
    @PostMapping("/repair")
    public Result<String> repairDataInconsistency(@RequestBody RepairRequest request) {
        try {
            switch (request.getType()) {
                case "application_status":
                    repairApplicationStatus(request.getIds());
                    break;
                case "cache_data":
                    repairCacheData(request.getIds());
                    break;
                case "process_data":
                    repairProcessData(request.getIds());
                    break;
                default:
                    return Result.error("未知的修复类型");
            }

            return Result.success("修复完成");
        } catch (Exception e) {
            log.error("数据修复失败", e);
            return Result.error("修复失败：" + e.getMessage());
        }
    }

    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            int count = applicationMapper.selectCount(null);
            long responseTime = System.currentTimeMillis() - startTime;

            dbHealth.put("status", "UP");
            dbHealth.put("responseTime", responseTime);
            dbHealth.put("recordCount", count);

            // 检查连接池状态
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                dbHealth.put("activeConnections", hikariDataSource.getActiveConnections());
                dbHealth.put("idleConnections", hikariDataSource.getIdleConnections());
                dbHealth.put("totalConnections", hikariDataSource.getTotalConnections());
            }

        } catch (Exception e) {
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }

        return dbHealth;
    }

    private Map<String, Object> checkRedisHealth() {
        Map<String, Object> redisHealth = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            redisTemplate.opsForValue().get("health_check");
            long responseTime = System.currentTimeMillis() - startTime;

            redisHealth.put("status", "UP");
            redisHealth.put("responseTime", responseTime);

        } catch (Exception e) {
            redisHealth.put("status", "DOWN");
            redisHealth.put("error", e.getMessage());
        }

        return redisHealth;
    }
}
```

**2. 应急处理预案**

```java
/**
 * 应急处理服务
 */
@Service
@Slf4j
public class EmergencyResponseService {

    @Resource
    private NotificationService notificationService;

    @Resource
    private VisitorApplicationService applicationService;

    /**
     * 系统降级处理
     */
    public void activateSystemDegradation(DegradationLevel level) {
        switch (level) {
            case LEVEL_1: // 轻度降级
                // 关闭非核心功能
                disableNonCriticalFeatures();
                break;

            case LEVEL_2: // 中度降级
                // 启用只读模式
                enableReadOnlyMode();
                break;

            case LEVEL_3: // 重度降级
                // 启用离线模式
                enableOfflineMode();
                break;

            case EMERGENCY: // 紧急模式
                // 系统维护模式
                enableMaintenanceMode();
                break;
        }

        // 通知相关人员
        notifyEmergencyResponse(level);
    }

    /**
     * 数据备份和恢复
     */
    public void performEmergencyBackup() {
        try {
            // 备份关键业务数据
            backupCriticalData();

            // 备份配置信息
            backupConfiguration();

            // 备份日志文件
            backupLogFiles();

            log.info("应急备份完成");

        } catch (Exception e) {
            log.error("应急备份失败", e);
            throw new ServiceException("应急备份失败");
        }
    }

    /**
     * 故障自动恢复
     */
    @EventListener
    public void handleSystemFailure(SystemFailureEvent event) {
        try {
            // 记录故障信息
            recordFailureInfo(event);

            // 尝试自动恢复
            boolean recovered = attemptAutoRecovery(event.getFailureType());

            if (recovered) {
                log.info("系统故障自动恢复成功：{}", event.getFailureType());
                notifyRecoverySuccess(event);
            } else {
                log.error("系统故障自动恢复失败：{}", event.getFailureType());
                escalateToManualIntervention(event);
            }

        } catch (Exception e) {
            log.error("故障处理异常", e);
            escalateToManualIntervention(event);
        }
    }

    /**
     * 流量控制
     */
    public void activateTrafficControl(TrafficControlLevel level) {
        switch (level) {
            case RATE_LIMIT:
                // 启用限流
                enableRateLimiting();
                break;

            case LOAD_BALANCE:
                // 调整负载均衡
                adjustLoadBalancing();
                break;

            case CIRCUIT_BREAKER:
                // 启用熔断器
                enableCircuitBreaker();
                break;
        }
    }

    private boolean attemptAutoRecovery(String failureType) {
        switch (failureType) {
            case "DATABASE_CONNECTION_FAILED":
                return recoverDatabaseConnection();

            case "REDIS_CONNECTION_FAILED":
                return recoverRedisConnection();

            case "EXTERNAL_SERVICE_TIMEOUT":
                return recoverExternalService();

            case "MEMORY_LEAK":
                return recoverMemoryLeak();

            default:
                return false;
        }
    }

    private boolean recoverDatabaseConnection() {
        try {
            // 重新初始化数据源
            reinitializeDataSource();

            // 测试连接
            testDatabaseConnection();

            return true;
        } catch (Exception e) {
            log.error("数据库连接恢复失败", e);
            return false;
        }
    }

    private void escalateToManualIntervention(SystemFailureEvent event) {
        // 发送紧急通知
        notificationService.sendEmergencyAlert(
            "系统故障需要人工干预",
            "故障类型：" + event.getFailureType() + "\n" +
            "故障时间：" + event.getTimestamp() + "\n" +
            "故障描述：" + event.getDescription()
        );

        // 记录到故障处理系统
        recordManualInterventionRequired(event);
    }
}
```

## 前端页面设计

### PC管理端页面设计

#### 访客申请管理页面 (visitor/application/index.vue)

**页面功能：**
- 访客申请列表查询和分页显示
- 多条件搜索和筛选
- 申请详情查看和编辑
- 审批操作和状态跟踪
- 数据导出和统计分析

**关键组件：**
- 搜索表单组件：支持申请单号、访客姓名、联系人、状态等条件
- 数据表格组件：显示申请列表，支持排序和分页
- 详情弹窗组件：显示完整的申请信息和审批记录
- 审批弹窗组件：提供审批操作界面
- 统计图表组件：显示访客流量和审批效率统计

#### 访客记录管理页面 (visitor/record/index.vue)

**页面功能：**
- 访客进出记录查询
- 实时监控在园访客状态
- 异常情况记录和处理
- 访客轨迹跟踪

### H5移动端页面设计

#### 访客申请页面 (visitor/apply.vue)

**页面功能：**
- 响应式表单设计，适配各种屏幕尺寸
- 分步骤填写，提升用户体验
- 实时表单验证和错误提示
- 图片上传和预览功能
- 申请进度跟踪

**关键特性：**
- 使用Vant UI组件库
- 支持拍照和相册选择
- 地理位置自动获取
- 离线数据缓存

#### 警卫操作页面 (guard/scan.vue)

**页面功能：**
- 二维码扫描功能
- 访客信息显示和核实
- 入园出园登记操作
- 异常情况快速记录

## 集成方案设计

### 企业微信集成

**集成方式：**
- 企业微信API接口调用
- WebHook事件接收
- 消息推送和通知

**通知场景：**
- 访客申请提交通知联系人
- 审批结果通知申请人
- 入园出园状态通知
- 异常情况紧急通知

### 二维码服务集成

**技术方案：**
- 基于JWT的安全二维码
- ZXing库生成二维码图片
- 支持离线验证和在线校验

**安全机制：**
- AES加密算法保护数据
- 数字签名防止篡改
- 时效性控制防止重复使用
- 权限范围限制访问区域

### 短信邮件服务

**短信服务：**
- 阿里云短信服务集成
- 模板化消息发送
- 发送频率限制和监控

**邮件服务：**
- SMTP协议支持
- HTML格式邮件模板
- 附件发送支持

## 测试用例设计

### 单元测试用例

**VisitorApplicationServiceTest.java**

```java
@SpringBootTest
@Transactional
class VisitorApplicationServiceTest {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Test
    @DisplayName("创建访客申请 - 成功")
    void testCreateApplication_Success() {
        // Given
        VisitorApplicationSaveReqVO reqVO = buildTestApplication();

        // When
        Long applicationId = visitorApplicationService.createApplication(reqVO);

        // Then
        assertThat(applicationId).isNotNull();
        VisitorApplicationDO application = visitorApplicationService.getApplication(applicationId);
        assertThat(application.getVisitorName()).isEqualTo("张三");
        assertThat(application.getStatus()).isEqualTo(VisitorStatusEnum.PENDING_CONFIRM.getStatus());
    }

    @Test
    @DisplayName("创建访客申请 - 访问时间无效")
    void testCreateApplication_InvalidVisitTime() {
        // Given
        VisitorApplicationSaveReqVO reqVO = buildTestApplication();
        reqVO.setVisitStartTime(LocalDateTime.now().plusDays(1));
        reqVO.setVisitEndTime(LocalDateTime.now().plusDays(1).minusHours(1));

        // When & Then
        assertThatThrownBy(() -> visitorApplicationService.createApplication(reqVO))
            .isInstanceOf(ServiceException.class)
            .hasMessageContaining("访问时间无效");
    }

    private VisitorApplicationSaveReqVO buildTestApplication() {
        VisitorApplicationSaveReqVO reqVO = new VisitorApplicationSaveReqVO();
        reqVO.setVisitorType(1);
        reqVO.setCompanyName("测试公司");
        reqVO.setVisitorName("张三");
        reqVO.setVisitorPhone("13800138000");
        reqVO.setVisitReason("商务洽谈");
        reqVO.setContactPerson("李四");
        reqVO.setContactPhone("13900139000");
        reqVO.setVisitStartTime(LocalDateTime.now().plusDays(1));
        reqVO.setVisitEndTime(LocalDateTime.now().plusDays(1).plusHours(4));
        reqVO.setVisitArea("A区");
        return reqVO;
    }
}
```

### 集成测试用例

**访客申请流程集成测试：**
1. 提交申请 → 验证申请创建成功
2. 联系人确认 → 验证状态更新
3. 部门审批 → 验证审批流程
4. 生成二维码 → 验证二维码有效性
5. 入园验证 → 验证扫码功能
6. 出园登记 → 验证流程完整性

### 性能测试用例

**并发申请测试：**
- 测试场景：100个并发用户同时提交访客申请
- 预期结果：响应时间 < 2秒，成功率 > 99%

**二维码生成性能测试：**
- 测试场景：批量生成1000个二维码
- 预期结果：平均生成时间 < 500ms

---

## 总结

访客管理模块详细设计文档涵盖了完整的业务流程、技术实现和测试方案，为开发团队提供了详细的实施指导。该模块具备以下特点：

1. **流程完整**：覆盖从申请到出园的完整访客管理流程
2. **安全可靠**：多重验证机制和安全防护措施
3. **用户友好**：PC端和移动端双重支持，操作简便
4. **集成性强**：与企业微信、短信等外部服务深度集成
5. **可扩展性**：模块化设计，便于功能扩展和维护

该设计方案为访客管理模块的开发实施提供了完整的技术指导和质量保障。
